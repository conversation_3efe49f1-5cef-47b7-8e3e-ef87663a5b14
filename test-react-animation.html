<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React兼容的FLIP动画测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            padding: 40px;
            background: #1a1a1a;
            color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        
        h1, h2 {
            color: #fff;
        }
        
        .instructions {
            background: #1e3a8a;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #065f46;
            color: #d1fae5;
        }
        
        /* 模拟 StyleManager 的样式 */
        .lu-tooltip {
            backdrop-filter: blur(14px) saturate(160%);
            -webkit-backdrop-filter: blur(14px) saturate(160%);
            background: rgba(30, 30, 30, 0.45);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 4px 12px;
            font-size: 0.88em;
            line-height: 1.45;
            color: #dadada;
            display: inline-block;
            white-space: nowrap;
            transition: color 0.15s ease-out;
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
            user-select: none;
        }
        
        .lu-pos {
            font-weight: 500;
            color: inherit;
            margin-right: 2px;
        }
        
        .lu-chinese-short {
            font-size: 0.9em;
            color: inherit;
            margin-right: 3px;
        }
        
        .lu-chinese-short.interactive {
            cursor: pointer;
            user-select: none;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.15s ease;
            pointer-events: auto;
        }
        
        .lu-chinese-short.interactive:hover {
            background-color: #ffffff !important;
            color: #333333 !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .lu-chinese-short.interactive:active {
            transform: scale(1.05) translateY(0);
            transition: transform 0.1s ease-out;
            background-color: #f0f0f0 !important;
        }
        
        .lu-separator {
            margin: 0 4px;
            opacity: 0.6;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            padding: 15px;
            border: 1px solid #444;
            border-radius: 8px;
            background: #333;
        }
        
        .method-card h3 {
            margin-top: 0;
            color: #4ade80;
        }
        
        .method-card.active {
            border-color: #4ade80;
            background: #1a2e1a;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 React兼容的FLIP动画测试</h1>
        
        <div class="instructions">
            <h3>🎯 测试目标</h3>
            <p>验证两种FLIP动画实现方案：</p>
            <p><strong>方案A</strong>: 纯JavaScript实现（当前test-animation.html的方案）</p>
            <p><strong>方案B</strong>: React兼容实现（使用requestAnimationFrame + 批量更新）</p>
        </div>
        
        <div class="comparison-grid">
            <div class="method-card active" id="method-a">
                <h3>方案A: 纯JavaScript</h3>
                <p>✅ 稳定的FLIP动画</p>
                <p>✅ 无React渲染干扰</p>
                <p>❌ 需要重构数据绑定</p>
            </div>
            
            <div class="method-card" id="method-b">
                <h3>方案B: React兼容</h3>
                <p>✅ 保持React生态</p>
                <p>✅ 使用requestAnimationFrame</p>
                <p>⚠️ 复杂度较高</p>
            </div>
        </div>
        
        <div class="status" id="status">
            🎬 准备测试React兼容的FLIP动画方案
        </div>
        
        <div class="test-section">
            <h2>🎯 测试用例：developed</h2>
            <div id="tooltip-container"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 性能对比</h2>
            <div id="performance-stats">
                <p>点击释义开始性能测试</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = {
            word: "developed",
            explain: [
                {
                    pos: "adjective",
                    definitions: [
                        { definition: "having developed", chinese: "发展的", chinese_short: "发展的" },
                        { definition: "mature", chinese: "成熟的", chinese_short: "成熟的" },
                        { definition: "advanced", chinese: "发达的", chinese_short: "发达的" }
                    ]
                },
                {
                    pos: "verb",
                    definitions: [
                        { definition: "to develop", chinese: "发展", chinese_short: "发展" }
                    ]
                }
            ]
        };

        let preferences = {};
        let isAnimating = false;
        let elementsMap = new Map();
        let performanceData = [];

        // React兼容的FLIP动画实现
        class ReactCompatibleFLIP {
            constructor() {
                this.animationFrame = null;
                this.isAnimating = false;
            }

            // 模拟React的批量更新机制
            batchedUpdate(callback) {
                // 使用requestAnimationFrame确保在下一帧执行
                if (this.animationFrame) {
                    cancelAnimationFrame(this.animationFrame);
                }
                
                this.animationFrame = requestAnimationFrame(() => {
                    callback();
                    this.animationFrame = null;
                });
            }

            // 改进的FLIP动画
            performAnimation(elementsMap, onComplete) {
                if (this.isAnimating) return;
                this.isAnimating = true;

                const startTime = performance.now();

                // 使用双重requestAnimationFrame确保DOM更新完成
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        this.executeFlipAnimation(elementsMap, startTime, onComplete);
                    });
                });
            }

            executeFlipAnimation(elementsMap, startTime, onComplete) {
                const newPositions = new Map();
                
                // 获取新位置
                elementsMap.forEach((element, key) => {
                    if (element.parentElement) {
                        newPositions.set(key, element.getBoundingClientRect());
                    }
                });

                // 应用FLIP动画
                elementsMap.forEach((element, key) => {
                    const oldPos = element.dataset.oldPosition;
                    const newPos = newPositions.get(key);
                    
                    if (oldPos && newPos) {
                        const oldRect = JSON.parse(oldPos);
                        const deltaX = oldRect.left - newPos.left;
                        const deltaY = oldRect.top - newPos.top;

                        if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
                            element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                            element.style.transition = 'none';
                            element.offsetHeight; // 强制重排
                            element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                            element.style.transform = 'translate(0, 0)';
                        }
                    }
                    delete element.dataset.oldPosition;
                });

                // 动画完成处理
                setTimeout(() => {
                    this.isAnimating = false;
                    elementsMap.forEach((element) => {
                        element.style.transition = '';
                        element.style.transform = '';
                    });
                    
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    performanceData.push(duration);
                    updatePerformanceStats();
                    
                    if (onComplete) onComplete();
                }, 300);
            }
        }

        const flipAnimator = new ReactCompatibleFLIP();

        // 其他函数保持不变...
        function recordPreference(word, pos, chineseShort) {
            const key = `${pos}.${chineseShort}`;
            if (!preferences[word]) preferences[word] = {};
            preferences[word][key] = (preferences[word][key] || 0) + 1;
        }

        function sortByPreference(word, explain) {
            if (!explain?.length) return explain;
            const prefs = preferences[word] || {};
            
            const groupScores = explain.map(group => {
                const maxScore = Math.max(
                    ...group.definitions.map(def => 
                        prefs[`${group.pos}.${def.chinese_short}`] || 0
                    )
                );
                return { ...group, maxScore };
            });
            
            groupScores.sort((a, b) => b.maxScore - a.maxScore);
            
            return groupScores.map(group => ({
                ...group,
                definitions: group.definitions.slice().sort((a, b) => {
                    const scoreA = prefs[`${group.pos}.${a.chinese_short}`] || 0;
                    const scoreB = prefs[`${group.pos}.${b.chinese_short}`] || 0;
                    return scoreB - scoreA;
                })
            }));
        }

        function handleDefinitionClick(event, pos, chineseShort) {
            if (isAnimating) return;
            
            event.stopPropagation();
            event.preventDefault();

            updateStatus('🎬 执行React兼容的FLIP动画...');

            // 记录位置
            elementsMap.forEach((element) => {
                if (element.parentElement) {
                    const rect = element.getBoundingClientRect();
                    element.dataset.oldPosition = JSON.stringify({
                        left: rect.left,
                        top: rect.top,
                        width: rect.width,
                        height: rect.height
                    });
                }
            });

            recordPreference(mockData.word, pos, chineseShort);
            isAnimating = true;
            
            // 使用批量更新模拟React行为
            flipAnimator.batchedUpdate(() => {
                renderTooltip();
                flipAnimator.performAnimation(elementsMap, () => {
                    isAnimating = false;
                    updateStatus('✅ React兼容动画完成');
                });
            });
        }

        function renderTooltip() {
            const container = document.getElementById('tooltip-container');
            const sortedExplain = sortByPreference(mockData.word, mockData.explain);
            
            elementsMap.clear();
            const parts = [];
            
            sortedExplain.forEach((item, index) => {
                const posShort = item.pos === "adjective" ? "adj." : 
                               item.pos === "verb" ? "v." : 
                               item.pos.substring(0, 3) + ".";
                
                parts.push(`<span class="lu-pos">${posShort}</span>`);
                
                item.definitions.forEach((def) => {
                    const stableKey = `${item.pos}-${def.chinese_short}`;
                    parts.push(`
                        <span 
                            class="lu-chinese-short interactive"
                            data-key="${stableKey}"
                            data-pos="${item.pos}"
                            data-chinese="${def.chinese_short}"
                        >
                            ${def.chinese_short}
                        </span>
                    `);
                });
                
                if (index < sortedExplain.length - 1) {
                    parts.push('<span class="lu-separator"> </span>');
                }
            });
            
            container.innerHTML = `
                <span class="lu-tooltip" data-theme="dark">
                    ${parts.join('')}
                </span>
            `;
            
            // 重新绑定事件
            container.querySelectorAll('.lu-chinese-short.interactive').forEach(el => {
                const key = el.dataset.key;
                const pos = el.dataset.pos;
                const chinese = el.dataset.chinese;
                
                elementsMap.set(key, el);
                el.addEventListener('click', (e) => handleDefinitionClick(e, pos, chinese));
            });
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function updatePerformanceStats() {
            const statsDiv = document.getElementById('performance-stats');
            if (performanceData.length === 0) {
                statsDiv.innerHTML = '<p>点击释义开始性能测试</p>';
                return;
            }
            
            const avg = performanceData.reduce((a, b) => a + b, 0) / performanceData.length;
            const min = Math.min(...performanceData);
            const max = Math.max(...performanceData);
            
            statsDiv.innerHTML = `
                <h3>性能统计 (${performanceData.length} 次动画):</h3>
                <p>平均耗时: ${avg.toFixed(2)}ms</p>
                <p>最快: ${min.toFixed(2)}ms</p>
                <p>最慢: ${max.toFixed(2)}ms</p>
            `;
        }

        // 初始化
        renderTooltip();
        updateStatus('✅ React兼容动画系统已加载');
    </script>
</body>
</html>
