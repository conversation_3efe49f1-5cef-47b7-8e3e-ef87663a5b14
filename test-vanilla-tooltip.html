<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VanillaTooltip 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            padding: 40px;
            background: #1a1a1a;
            color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        
        h1, h2 {
            color: #fff;
        }
        
        .instructions {
            background: #1e3a8a;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #065f46;
            color: #d1fae5;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #444;
            border-color: #666;
        }
        
        .btn.active {
            background: #4ade80;
            color: #000;
            border-color: #4ade80;
        }
        
        .tooltip-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px dashed #555;
            border-radius: 8px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .performance-stats {
            background: #2d1b69;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .performance-stats h3 {
            margin-top: 0;
            color: #a78bfa;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .stat-item {
            background: #1e1b4b;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #c4b5fd;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #a78bfa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 VanillaTooltip 完整测试</h1>
        
        <div class="instructions">
            <h3>🎯 测试说明</h3>
            <p>这是纯JavaScript实现的Tooltip，完全避免了React重新渲染的问题</p>
            <p>1. 点击下面的控制按钮切换不同的测试数据</p>
            <p>2. 点击中文释义观察FLIP动画效果</p>
            <p>3. 查看性能统计数据</p>
        </div>
        
        <div class="status" id="status">
            ✅ VanillaTooltip 系统已加载
        </div>
        
        <div class="controls">
            <button class="btn active" data-word="developed">developed</button>
            <button class="btn" data-word="beautiful">beautiful</button>
            <button class="btn" data-word="important">important</button>
            <button class="btn" data-word="understand">understand</button>
            <button class="btn" id="toggle-interactive">切换交互模式</button>
            <button class="btn" id="toggle-theme">切换主题</button>
            <button class="btn" id="reset-preferences">重置偏好</button>
        </div>
        
        <div class="test-section">
            <h2>🎯 Tooltip 展示区</h2>
            <div class="tooltip-container" id="tooltip-container">
                <!-- VanillaTooltip 将在这里渲染 -->
            </div>
        </div>
        
        <div class="performance-stats">
            <h3>📊 性能统计</h3>
            <div id="performance-display">
                <p>开始点击释义来收集性能数据...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 技术特性</h2>
            <ul>
                <li>✅ 纯JavaScript实现，无React渲染干扰</li>
                <li>✅ 完美的FLIP动画，无瞬移问题</li>
                <li>✅ 与StyleManager集成，统一样式管理</li>
                <li>✅ 支持偏好记录和智能排序</li>
                <li>✅ 性能监控和统计</li>
                <li>✅ 完整的TypeScript类型支持</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // 模拟测试数据
        const testData = {
            developed: {
                word: "developed",
                explain: [
                    {
                        pos: "adjective",
                        definitions: [
                            { definition: "having developed", chinese: "发展的", chinese_short: "发展的" },
                            { definition: "mature", chinese: "成熟的", chinese_short: "成熟的" },
                            { definition: "advanced", chinese: "发达的", chinese_short: "发达的" }
                        ]
                    },
                    {
                        pos: "verb",
                        definitions: [
                            { definition: "to develop", chinese: "发展", chinese_short: "发展" }
                        ]
                    }
                ]
            },
            beautiful: {
                word: "beautiful",
                explain: [
                    {
                        pos: "adjective",
                        definitions: [
                            { definition: "pleasing to look at", chinese: "美丽的", chinese_short: "美丽的" },
                            { definition: "attractive", chinese: "漂亮的", chinese_short: "漂亮的" },
                            { definition: "gorgeous", chinese: "绚丽的", chinese_short: "绚丽的" }
                        ]
                    }
                ]
            },
            important: {
                word: "important",
                explain: [
                    {
                        pos: "adjective",
                        definitions: [
                            { definition: "of great significance", chinese: "重要的", chinese_short: "重要的" },
                            { definition: "crucial", chinese: "关键的", chinese_short: "关键的" },
                            { definition: "essential", chinese: "必要的", chinese_short: "必要的" }
                        ]
                    }
                ]
            },
            understand: {
                word: "understand",
                explain: [
                    {
                        pos: "verb",
                        definitions: [
                            { definition: "to comprehend", chinese: "理解", chinese_short: "理解" },
                            { definition: "to grasp", chinese: "掌握", chinese_short: "掌握" },
                            { definition: "to know", chinese: "知道", chinese_short: "知道" }
                        ]
                    }
                ]
            }
        };

        // 模拟VanillaTooltip类（简化版，用于演示）
        class MockVanillaTooltip {
            constructor(container, data, options = {}) {
                this.container = container;
                this.data = data;
                this.options = { theme: "dark", interactive: false, ...options };
                this.elementsMap = new Map();
                this.isAnimating = false;
                this.performanceData = [];
                
                this.init();
            }

            init() {
                this.injectStyles();
                this.render();
            }

            injectStyles() {
                if (document.getElementById('mock-tooltip-styles')) return;

                const styles = `
                    .lu-tooltip {
                        backdrop-filter: blur(14px) saturate(160%);
                        -webkit-backdrop-filter: blur(14px) saturate(160%);
                        background: rgba(30, 30, 30, 0.45);
                        border: 1px solid rgba(255, 255, 255, 0.15);
                        border-radius: 8px;
                        padding: 4px 12px;
                        font-size: 0.88em;
                        line-height: 1.45;
                        color: #dadada;
                        display: inline-block;
                        white-space: nowrap;
                        transition: color 0.15s ease-out;
                        box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
                        user-select: none;
                    }
                    
                    .lu-tooltip[data-theme="light"] {
                        background: rgba(255, 255, 255, 0.9);
                        color: #333;
                        border-color: rgba(0, 0, 0, 0.1);
                    }
                    
                    .lu-pos {
                        font-weight: 500;
                        color: inherit;
                        margin-right: 2px;
                    }
                    
                    .lu-chinese-short {
                        font-size: 0.9em;
                        color: inherit;
                        margin-right: 3px;
                    }
                    
                    .lu-chinese-short.interactive {
                        cursor: pointer;
                        user-select: none;
                        padding: 2px 4px;
                        border-radius: 3px;
                        transition: all 0.15s ease;
                        pointer-events: auto;
                    }
                    
                    .lu-chinese-short.interactive:hover {
                        background-color: #ffffff !important;
                        color: #333333 !important;
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }
                    
                    .lu-separator {
                        margin: 0 4px;
                        opacity: 0.6;
                    }
                `;

                const styleElement = document.createElement('style');
                styleElement.id = 'mock-tooltip-styles';
                styleElement.textContent = styles;
                document.head.appendChild(styleElement);
            }

            render() {
                const { word, explain = [] } = this.data;
                const { interactive, theme } = this.options;

                this.container.innerHTML = '';
                this.elementsMap.clear();

                if (!explain.length) {
                    this.container.innerHTML = `<span class="lu-tooltip" data-theme="${theme}">暂无释义</span>`;
                    return;
                }

                const parts = [];
                
                explain.forEach((item, index) => {
                    const posShort = this.getPosShort(item.pos);
                    parts.push(`<span class="lu-pos">${posShort}</span>`);
                    
                    item.definitions.forEach((def) => {
                        const stableKey = `${item.pos}-${def.chinese_short}`;
                        const interactiveClass = interactive ? 'interactive' : '';
                        
                        parts.push(`
                            <span 
                                class="lu-chinese-short ${interactiveClass}"
                                data-key="${stableKey}"
                                data-pos="${item.pos}"
                                data-chinese="${def.chinese_short}"
                            >
                                ${def.chinese_short}
                            </span>
                        `);
                    });
                    
                    if (index < explain.length - 1) {
                        parts.push('<span class="lu-separator"> </span>');
                    }
                });

                this.container.innerHTML = `
                    <span class="lu-tooltip" data-theme="${theme}">
                        ${parts.join('')}
                    </span>
                `;

                if (interactive) {
                    this.bindEvents();
                }
            }

            bindEvents() {
                const elements = this.container.querySelectorAll('.lu-chinese-short.interactive');
                
                elements.forEach((element) => {
                    const key = element.dataset.key;
                    const pos = element.dataset.pos;
                    const chinese = element.dataset.chinese;
                    
                    this.elementsMap.set(key, element);
                    
                    element.addEventListener('click', (e) => {
                        this.handleClick(e, pos, chinese);
                    });
                });
            }

            handleClick(event, pos, chineseShort) {
                if (this.isAnimating) return;

                const startTime = performance.now();
                
                event.stopPropagation();
                event.preventDefault();

                console.log(`👆 Clicked: ${this.data.word} -> ${pos}.${chineseShort}`);
                
                // 记录位置
                this.recordPositions();
                
                // 点击反馈
                this.addClickFeedback(event.currentTarget);
                
                this.isAnimating = true;
                
                // 模拟重新排序（简化版）
                this.simulateReorder();
                
                // 执行动画
                requestAnimationFrame(() => {
                    this.performFLIP();
                    
                    setTimeout(() => {
                        this.isAnimating = false;
                        const endTime = performance.now();
                        this.performanceData.push(endTime - startTime);
                        updatePerformanceDisplay();
                        updateStatus('✅ 动画完成');
                    }, 300);
                });

                if (this.options.onPreferenceUpdate) {
                    this.options.onPreferenceUpdate();
                }
            }

            recordPositions() {
                this.elementsMap.forEach((element) => {
                    if (element.parentElement) {
                        const rect = element.getBoundingClientRect();
                        element.dataset.oldPosition = JSON.stringify({
                            left: rect.left,
                            top: rect.top
                        });
                    }
                });
            }

            addClickFeedback(element) {
                element.style.transform = 'scale(1.1)';
                element.style.transition = 'transform 0.1s ease-out';
                
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = '';
                }, 100);
            }

            simulateReorder() {
                // 简化的重新排序逻辑
                const container = this.container.querySelector('.lu-tooltip');
                const elements = Array.from(container.children);
                
                // 随机重排序（仅用于演示）
                const shuffled = elements.sort(() => Math.random() - 0.5);
                shuffled.forEach(el => container.appendChild(el));
            }

            performFLIP() {
                this.elementsMap.forEach((element, key) => {
                    const oldPos = element.dataset.oldPosition;
                    if (!oldPos) return;

                    const oldRect = JSON.parse(oldPos);
                    const newRect = element.getBoundingClientRect();
                    
                    const deltaX = oldRect.left - newRect.left;
                    const deltaY = oldRect.top - newRect.top;

                    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
                        element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                        element.style.transition = 'none';
                        element.offsetHeight;
                        element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                        element.style.transform = 'translate(0, 0)';
                    }

                    delete element.dataset.oldPosition;
                });
            }

            getPosShort(pos) {
                const posMap = {
                    'noun': 'n.',
                    'verb': 'v.',
                    'adjective': 'adj.',
                    'adverb': 'adv.'
                };
                return posMap[pos] || pos.substring(0, 3) + '.';
            }

            updateData(newData) {
                this.data = newData;
                this.render();
            }

            updateOptions(newOptions) {
                this.options = { ...this.options, ...newOptions };
                this.render();
            }

            getPerformanceData() {
                return this.performanceData;
            }
        }

        // 全局状态
        let currentTooltip = null;
        let currentWord = 'developed';
        let isInteractive = true;
        let currentTheme = 'dark';

        // 初始化
        function init() {
            const container = document.getElementById('tooltip-container');
            currentTooltip = new MockVanillaTooltip(
                container,
                testData[currentWord],
                {
                    theme: currentTheme,
                    interactive: isInteractive,
                    onPreferenceUpdate: () => {
                        updateStatus('🎯 偏好已更新');
                    }
                }
            );
            
            bindControlEvents();
            updateStatus('✅ VanillaTooltip 初始化完成');
        }

        function bindControlEvents() {
            // 单词切换
            document.querySelectorAll('[data-word]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const word = e.target.dataset.word;
                    switchWord(word);
                    
                    // 更新按钮状态
                    document.querySelectorAll('[data-word]').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                });
            });

            // 交互模式切换
            document.getElementById('toggle-interactive').addEventListener('click', () => {
                isInteractive = !isInteractive;
                currentTooltip.updateOptions({ interactive: isInteractive });
                updateStatus(`🔄 交互模式: ${isInteractive ? '开启' : '关闭'}`);
            });

            // 主题切换
            document.getElementById('toggle-theme').addEventListener('click', () => {
                currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
                currentTooltip.updateOptions({ theme: currentTheme });
                updateStatus(`🎨 主题切换为: ${currentTheme}`);
            });

            // 重置偏好
            document.getElementById('reset-preferences').addEventListener('click', () => {
                // 这里应该清理localStorage中的偏好数据
                updateStatus('🔄 偏好数据已重置');
            });
        }

        function switchWord(word) {
            if (testData[word]) {
                currentWord = word;
                currentTooltip.updateData(testData[word]);
                updateStatus(`📝 切换到单词: ${word}`);
            }
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function updatePerformanceDisplay() {
            const data = currentTooltip.getPerformanceData();
            const display = document.getElementById('performance-display');
            
            if (data.length === 0) {
                display.innerHTML = '<p>开始点击释义来收集性能数据...</p>';
                return;
            }

            const avg = data.reduce((a, b) => a + b, 0) / data.length;
            const min = Math.min(...data);
            const max = Math.max(...data);

            display.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.length}</div>
                        <div class="stat-label">动画次数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${avg.toFixed(1)}ms</div>
                        <div class="stat-label">平均耗时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${min.toFixed(1)}ms</div>
                        <div class="stat-label">最快</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${max.toFixed(1)}ms</div>
                        <div class="stat-label">最慢</div>
                    </div>
                </div>
            `;
        }

        // 启动应用
        init();
    </script>
</body>
</html>
