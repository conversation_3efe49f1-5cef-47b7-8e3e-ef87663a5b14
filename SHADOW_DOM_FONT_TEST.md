# Shadow DOM 字体测试指南

## 测试目标 🎯

验证 Noto Sans SC 字体（字重 200）是否正确应用到 Lucid Slider 的 Shadow DOM 内部，而不影响宿主页面。

## 测试步骤 📋

### 1. 基础字体测试

1. **打开测试页面**
   - 访问任意网页（如 Google Blog）
   - 打开浏览器开发者工具

2. **激活 Lucid Slider**
   - 使用快捷键或点击扩展图标
   - 确保 Slider 正常显示

3. **检查字体应用**
   ```javascript
   // 在控制台执行以下代码
   const slider = document.querySelector('lucid-slider');
   const shadowRoot = slider.shadowRoot;
   const slideElement = shadowRoot.querySelector('.lu-slide');
   
   // 检查字体设置
   const computedStyle = getComputedStyle(slideElement);
   console.log('Font Family:', computedStyle.fontFamily);
   console.log('Font Weight:', computedStyle.fontWeight);
   ```

4. **预期结果**
   - Font Family: 应包含 "Noto Sans SC"
   - Font Weight: 应为 "200" 或 "extralight"

### 2. 字体隔离测试

1. **检查宿主页面字体**
   ```javascript
   // 检查宿主页面的字体设置
   const bodyStyle = getComputedStyle(document.body);
   console.log('Host Page Font:', bodyStyle.fontFamily);
   console.log('Host Page Weight:', bodyStyle.fontWeight);
   ```

2. **对比验证**
   - 宿主页面字体应保持不变
   - Shadow DOM 内部应使用 Noto Sans SC

### 3. 组件字体测试

测试各个组件的字体应用：

```javascript
const shadowRoot = document.querySelector('lucid-slider').shadowRoot;

// 测试标题字体
const title = shadowRoot.querySelector('h1');
if (title) {
  const titleStyle = getComputedStyle(title);
  console.log('Title Font:', titleStyle.fontFamily);
  console.log('Title Weight:', titleStyle.fontWeight); // 应为 300
}

// 测试按钮字体
const button = shadowRoot.querySelector('.lu-social-login-btn');
if (button) {
  const buttonStyle = getComputedStyle(button);
  console.log('Button Font:', buttonStyle.fontFamily);
  console.log('Button Weight:', buttonStyle.fontWeight); // 应为 200
}

// 测试输入框字体
const input = shadowRoot.querySelector('.lu-email-input');
if (input) {
  const inputStyle = getComputedStyle(input);
  console.log('Input Font:', inputStyle.fontFamily);
  console.log('Input Weight:', inputStyle.fontWeight); // 应为 200
}

// 测试错误消息字体
const error = shadowRoot.querySelector('.lu-error-message');
if (error) {
  const errorStyle = getComputedStyle(error);
  console.log('Error Font:', errorStyle.fontFamily);
  console.log('Error Weight:', errorStyle.fontWeight); // 应为 200
}
```

### 4. 网络请求测试

1. **检查字体加载**
   - 打开 Network 标签
   - 刷新页面
   - 查找 Google Fonts 请求

2. **预期请求**
   ```
   https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap
   ```

3. **验证字体文件下载**
   - 确认字体 CSS 文件加载成功
   - 确认字体 woff2 文件下载完成

## 测试用例 ✅

### 用例 1: 基础字体应用
- **输入**: 打开 Lucid Slider
- **预期**: Shadow DOM 内所有文字使用 Noto Sans SC，字重 200
- **验证**: 使用开发者工具检查 computed styles

### 用例 2: 字体隔离
- **输入**: 在有自定义字体的网页上打开 Slider
- **预期**: 宿主页面字体不受影响，Slider 内部使用 Noto Sans SC
- **验证**: 对比宿主页面和 Shadow DOM 的字体设置

### 用例 3: 字重层次
- **输入**: 检查不同组件的字重
- **预期**: 
  - 普通文字: 200
  - 标题: 300
  - Logo: 700
  - 统计数字: 600
- **验证**: 检查各组件的 font-weight 属性

### 用例 4: 响应式字体
- **输入**: 调整浏览器窗口大小
- **预期**: 字体大小根据媒体查询调整，字体家族保持不变
- **验证**: 检查不同屏幕尺寸下的字体设置

## 故障排除 🔧

### 问题 1: 字体未加载
**症状**: Shadow DOM 内显示备用字体
**解决**: 
1. 检查网络连接
2. 确认 Google Fonts 可访问
3. 检查 CSP 策略是否阻止字体加载

### 问题 2: 字重显示异常
**症状**: 字体显示过粗或过细
**解决**:
1. 确认 font-weight 值正确
2. 检查 CSS 变量定义
3. 验证字体文件是否包含对应字重

### 问题 3: 字体影响宿主页面
**症状**: 宿主页面字体被改变
**解决**:
1. 检查是否有全局 CSS 泄露
2. 确认样式只在 Shadow DOM 内定义
3. 移除任何全局字体设置

## 自动化测试脚本 🤖

```javascript
// 完整的字体测试脚本
function testLucidSliderFonts() {
  const results = {
    shadowDomExists: false,
    fontFamily: null,
    fontWeight: null,
    hostPageUnaffected: true,
    componentsCorrect: {}
  };

  // 检查 Shadow DOM
  const slider = document.querySelector('lucid-slider');
  if (!slider || !slider.shadowRoot) {
    console.error('❌ Shadow DOM not found');
    return results;
  }
  
  results.shadowDomExists = true;
  const shadowRoot = slider.shadowRoot;

  // 检查主容器字体
  const slideElement = shadowRoot.querySelector('.lu-slide');
  if (slideElement) {
    const style = getComputedStyle(slideElement);
    results.fontFamily = style.fontFamily;
    results.fontWeight = style.fontWeight;
    
    console.log('✅ Slide Font Family:', style.fontFamily);
    console.log('✅ Slide Font Weight:', style.fontWeight);
  }

  // 检查宿主页面是否受影响
  const hostStyle = getComputedStyle(document.body);
  const hostFont = hostStyle.fontFamily;
  if (hostFont.includes('Noto Sans SC')) {
    results.hostPageUnaffected = false;
    console.warn('⚠️ Host page font affected');
  } else {
    console.log('✅ Host page font unaffected');
  }

  // 检查各组件字体
  const components = {
    title: 'h1',
    button: '.lu-social-login-btn',
    input: '.lu-email-input',
    error: '.lu-error-message'
  };

  Object.entries(components).forEach(([name, selector]) => {
    const element = shadowRoot.querySelector(selector);
    if (element) {
      const style = getComputedStyle(element);
      results.componentsCorrect[name] = {
        fontFamily: style.fontFamily,
        fontWeight: style.fontWeight
      };
      console.log(`✅ ${name} Font:`, style.fontFamily, style.fontWeight);
    }
  });

  return results;
}

// 运行测试
testLucidSliderFonts();
```

## 预期输出 📊

正确配置后，测试脚本应输出：

```
✅ Slide Font Family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
✅ Slide Font Weight: 200
✅ Host page font unaffected
✅ title Font: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif 300
✅ button Font: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif 200
✅ input Font: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif 200
✅ error Font: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif 200
```
