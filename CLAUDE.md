# CLAUDE.md

## Guidance Documents

Project-specific guidance documents for AI assistants:

@.claude/steering/project-summary.md
@.claude/steering/architecture-analysis.md
@.claude/steering/architecture-diagrams.md
@.claude/steering/design-patterns-analysis.md
@.claude/steering/tech-stack-analysis.md


## AI Assistant Guidance

- **Browser Extension Development Reminders**:
  - 记住 这个是浏览器插件 不要给我脚本在console里面跑的指令

## Translation and Language Support

- **中文回复**: 确保在进行代码和文档交互时，能够流畅地使用中文进行沟通和技术讨论

## API Testing Notes

- 记住 如果需要API测试目前只能在background测试

- **测试驱动开发 (Test-Driven Development)**:
  - 新功能开发之前需要创建健全的测试脚本
  - 遵循测试 - 开发 - 测试 - 用户界面测试的开发流程

## Work Process Guidance

- 在执行任务前 先给我你的处理思路 审批没问题后 再操作