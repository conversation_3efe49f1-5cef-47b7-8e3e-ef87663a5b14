# Lucid Extension Auth 系统架构图

## 1. 整体系统架构图

```mermaid
graph TB
    subgraph "Browser Layer"
        U[User Interface]
        D[DOM Elements]
        S[Storage]
    end
    
    subgraph "Extension Layer"
        subgraph "Popup UI"
            P[React Components]
            L[Login/Register Views]
            A[Account/Settings Views]
        end
        
        subgraph "Content Script"
            TM[Translation Manager]
            HM[Highlight Manager]
            TTM[Tooltip Manager]
            SM[Slider Manager]
            IH[Interaction Handlers]
        end
        
        subgraph "Background Script"
            BH[Message Handler]
            AM[Auth Manager]
            TP[Translation Proxy]
            DP[Dictionary Proxy]
            CM[Context Menu]
            LM[Lifecycle Manager]
        end
    end
    
    subgraph "External Services"
        BA[Backend API]
        TA[Translation APIs]
        DA[Dictionary API]
    end
    
    %% Popup UI Connections
    P --> BH
    L --> AM
    A --> AM
    
    %% Content Script Connections
    TM --> D
    HM --> D
    TTM --> D
    SM --> D
    IH --> D
    
    %% Background Script Connections
    BH --> TM
    BH --> HM
    BH --> TTM
    BH --> SM
    AM --> BA
    TP --> TA
    DP --> DA
    CM --> U
    
    %% Storage Connections
    AM --> S
    TM --> S
    P --> S
    
    %% Style
    classDef component fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef external fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class P,L,A,TM,HM,TTM,SM,IH component
    class BH,AM,TP,DP,CM,LM service
    class BA,TA,DA external
```

## 2. 认证系统架构图

```mermaid
graph TB
    subgraph "Authentication Components"
        UI[UI Components]
        Hook[useAuth Hook]
        AM[AuthManager]
        Storage[Storage]
        API[Backend API]
    end
    
    subgraph "AuthManager Internal"
        State[AuthState]
        Tokens[Token Management]
        Events[Event System]
        Validation[Validation Logic]
    end
    
    subgraph "Data Flow"
        Login[Login Flow]
        Register[Register Flow]
        Logout[Logout Flow]
        Refresh[Token Refresh]
    end
    
    %% Connections
    UI --> Hook
    Hook --> AM
    AM --> State
    AM --> Tokens
    AM --> Events
    AM --> Validation
    AM --> Storage
    AM --> API
    
    State --> Login
    State --> Register
    State --> Logout
    Tokens --> Refresh
    
    %% Style
    classDef component fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef internal fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef flow fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class UI,Hook,AM component
    class State,Tokens,Events,Validation internal
    class Login,Register,Logout,Refresh flow
```

## 3. 翻译系统架构图

```mermaid
graph TB
    subgraph "Translation System"
        TS[TranslateService]
        EM[Engine Manager]
        CM[Cache Manager]
        CFG[Config Manager]
        IR[Injection Rules]
    end
    
    subgraph "Translation Engines"
        GE[Google Engine]
        ME[Microsoft Engine]
        CE[Custom Engine]
    end
    
    subgraph "Text Processing"
        SC[DOM Scanner]
        TE[Text Extractor]
        TI[Text Injector]
        QC[Quality Checker]
    end
    
    subgraph "Data Flow"
        Input[Input Text]
        Process[Translation Process]
        Output[Translated Text]
    end
    
    %% Connections
    TS --> EM
    TS --> CM
    TS --> CFG
    TS --> IR
    
    EM --> GE
    EM --> ME
    EM --> CE
    
    SC --> TE
    TE --> Process
    Process --> TI
    TI --> QC
    
    Input --> SC
    Output --> QC
    
    %% Style
    classDef service fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef engine fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef process fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef flow fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class TS,EM,CM,CFG,IR service
    class GE,ME,CE engine
    class SC,TE,TI,QC process
    class Input,Process,Output flow
```

## 4. UI 组件架构图

```mermaid
graph TB
    subgraph "Main Components"
        Slider[Slider Component]
        Tooltip[DynamicTooltip]
        Highlight[Highlight Manager]
    end
    
    subgraph "Slider Sub-components"
        Login[LoginView]
        Register[RegisterView]
        Account[AccountView]
        Settings[SettingsView]
        My[MyView]
        Learn[LearnView]
    end
    
    subgraph "Tooltip Sub-components"
        Skeleton[TooltipSkeleton]
        Error[TooltipError]
        Content[TooltipContent]
    end
    
    subgraph "State Management"
        AuthState[Auth State]
        UIState[UI State]
        ConfigState[Config State]
    end
    
    subgraph "Event Handling"
        ClickEvents[Click Events]
        HoverEvents[Hover Events]
        KeyboardEvents[Keyboard Events]
    end
    
    %% Connections
    Slider --> Login
    Slider --> Register
    Slider --> Account
    Slider --> Settings
    Slider --> My
    Slider --> Learn
    
    Tooltip --> Skeleton
    Tooltip --> Error
    Tooltip --> Content
    
    Slider --> AuthState
    Tooltip --> UIState
    Highlight --> ConfigState
    
    Login --> ClickEvents
    Tooltip --> HoverEvents
    Slider --> KeyboardEvents
    
    %% Style
    classDef main fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef sub fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef state fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef event fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class Slider,Tooltip,Highlight main
    class Login,Register,Account,Settings,My,Learn,Skeleton,Error,Content sub
    class AuthState,UIState,ConfigState state
    class ClickEvents,HoverEvents,KeyboardEvents event
```

## 5. 消息通信架构图

```mermaid
graph TB
    subgraph "Message Sources"
        Popup[Popup UI]
        Content[Content Script]
        Background[Background Script]
        External[External APIs]
    end
    
    subgraph "Message Types"
        AuthMsg[Auth Messages]
        TransMsg[Translation Messages]
        DictMsg[Dictionary Messages]
        UIMsg[UI Messages]
    end
    
    subgraph "Message Handlers"
        AuthHandler[Auth Handler]
        TransHandler[Translation Handler]
        DictHandler[Dictionary Handler]
        UIHandler[UI Handler]
    end
    
    subgraph "Response Flow"
        Success[Success Response]
        Error[Error Response]
        Progress[Progress Response]
    end
    
    %% Connections
    Popup --> AuthMsg
    Popup --> UIMsg
    Content --> TransMsg
    Content --> DictMsg
    Background --> TransMsg
    Background --> DictMsg
    External --> TransMsg
    External --> DictMsg
    
    AuthMsg --> AuthHandler
    TransMsg --> TransHandler
    DictMsg --> DictHandler
    UIMsg --> UIHandler
    
    AuthHandler --> Success
    AuthHandler --> Error
    TransHandler --> Success
    TransHandler --> Error
    TransHandler --> Progress
    DictHandler --> Success
    DictHandler --> Error
    UIHandler --> Success
    UIHandler --> Error
    
    %% Style
    classDef source fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef msg fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef response fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class Popup,Content,Background,External source
    class AuthMsg,TransMsg,DictMsg,UIMsg msg
    class AuthHandler,TransHandler,DictHandler,UIHandler handler
    class Success,Error,Progress response
```

## 6. 数据流架构图

```mermaid
graph TB
    subgraph "Input Layer"
        UserInput[User Input]
        DOMEvents[DOM Events]
        APIResponses[API Responses]
    end
    
    subgraph "Processing Layer"
        Validation[Data Validation]
        Transformation[Data Transformation]
        BusinessLogic[Business Logic]
        Caching[Caching Layer]
    end
    
    subgraph "Storage Layer"
        LocalStorage[Local Storage]
        SessionStorage[Session Storage]
        MemoryCache[Memory Cache]
        RemoteStorage[Remote Storage]
    end
    
    subgraph "Output Layer"
        UIUpdates[UI Updates]
        DOMChanges[DOM Changes]
        Notifications[Notifications]
        APICalls[API Calls]
    end
    
    %% Connections
    UserInput --> Validation
    DOMEvents --> Validation
    APIResponses --> Validation
    
    Validation --> Transformation
    Transformation --> BusinessLogic
    BusinessLogic --> Caching
    
    Caching --> LocalStorage
    Caching --> SessionStorage
    Caching --> MemoryCache
    Caching --> RemoteStorage
    
    BusinessLogic --> UIUpdates
    BusinessLogic --> DOMChanges
    BusinessLogic --> Notifications
    BusinessLogic --> APICalls
    
    %% Bidirectional connections
    LocalStorage --> BusinessLogic
    SessionStorage --> BusinessLogic
    MemoryCache --> BusinessLogic
    RemoteStorage --> BusinessLogic
    
    %% Style
    classDef input fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef output fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class UserInput,DOMEvents,APIResponses input
    class Validation,Transformation,BusinessLogic,Caching process
    class LocalStorage,SessionStorage,MemoryCache,RemoteStorage storage
    class UIUpdates,DOMChanges,Notifications,APICalls output
```

## 7. 模块依赖关系图

```mermaid
graph TB
    subgraph "Core Modules"
        Auth[Auth Module]
        Translation[Translation Module]
        UI[UI Module]
        Utils[Utils Module]
    end
    
    subgraph "Auth Dependencies"
        AuthTypes[Auth Types]
        AuthManager[Auth Manager]
        AuthHook[Auth Hook]
        AuthAPI[Auth API]
    end
    
    subgraph "Translation Dependencies"
        TransTypes[Translation Types]
        TransService[Translation Service]
        TransEngine[Translation Engine]
        TransCache[Translation Cache]
    end
    
    subgraph "UI Dependencies"
        Components[React Components]
        Styles[CSS Styles]
        Icons[Icon Components]
        Hooks[Custom Hooks]
    end
    
    subgraph "Utils Dependencies"
        ErrorHandler[Error Handler]
        Logger[Logger]
        Storage[Storage Utils]
        Validation[Validation Utils]
    end
    
    %% Core Module Dependencies
    Auth --> AuthTypes
    Auth --> AuthManager
    Auth --> AuthHook
    Auth --> AuthAPI
    Auth --> Utils
    
    Translation --> TransTypes
    Translation --> TransService
    Translation --> TransEngine
    Translation --> TransCache
    Translation --> Utils
    
    UI --> Components
    UI --> Styles
    UI --> Icons
    UI --> Hooks
    UI --> Auth
    UI --> Utils
    
    Utils --> ErrorHandler
    Utils --> Logger
    Utils --> Storage
    Utils --> Validation
    
    %% Cross Dependencies
    TransService --> ErrorHandler
    AuthManager --> Storage
    Components --> AuthHook
    
    %% Style
    classDef core fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef auth fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef trans fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef ui fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef utils fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class Auth,Translation,UI,Utils core
    class AuthTypes,AuthManager,AuthHook,AuthAPI auth
    class TransTypes,TransService,TransEngine,TransCache trans
    class Components,Styles,Icons,Hooks ui
    class ErrorHandler,Logger,Storage,Validation utils
```

## 8. 部署架构图

```mermaid
graph TB
    subgraph "Development Environment"
        DevCode[Source Code]
        DevTools[Dev Tools]
        TestEnv[Test Environment]
    end
    
    subgraph "Build Process"
        Build[Build Process]
        Bundle[Bundle]
        Optimize[Optimize]
        Package[Package]
    end
    
    subgraph "Browser Extensions"
        Chrome[Chrome Extension]
        Firefox[Firefox Extension]
        Safari[Safari Extension]
        Edge[Edge Extension]
    end
    
    subgraph "Distribution"
        Store[Extension Store]
        Direct[Direct Distribution]
        AutoUpdate[Auto Update]
    end
    
    subgraph "Monitoring"
        Analytics[Analytics]
        ErrorTracking[Error Tracking]
        Performance[Performance Monitoring]
    end
    
    %% Connections
    DevCode --> Build
    DevTools --> TestEnv
    TestEnv --> Build
    
    Build --> Bundle
    Bundle --> Optimize
    Optimize --> Package
    
    Package --> Chrome
    Package --> Firefox
    Package --> Safari
    Package --> Edge
    
    Chrome --> Store
    Firefox --> Store
    Safari --> Store
    Edge --> Store
    
    Store --> AutoUpdate
    Direct --> AutoUpdate
    
    Chrome --> Analytics
    Firefox --> Analytics
    Safari --> Analytics
    Edge --> Analytics
    
    Analytics --> ErrorTracking
    ErrorTracking --> Performance
    
    %% Style
    classDef dev fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef build fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef browser fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dist fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef monitor fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class DevCode,DevTools,TestEnv dev
    class Build,Bundle,Optimize,Package build
    class Chrome,Firefox,Safari,Edge browser
    class Store,Direct,AutoUpdate dist
    class Analytics,ErrorTracking,Performance monitor