/**
 * Translation Pipeline Types
 * 翻译流水线核心类型定义
 */

import type { TranslateFormat } from '../translate/types';
import { TaskPriority } from '../../utils/promise-pool';
import { InjectionStrategy } from '../../core/injection-rules';

// Re-export types that are used by other modules
export type { TranslateFormat };

/**
 * 扫描的节点信息
 */
export interface ScannedNode {
  /** HTML元素 */
  element: HTMLElement;
  /** 提取的文本内容 */
  text: string;
  /** HTML结构内容（如果包含HTML） */
  htmlContent?: string;
  /** 是否包含HTML结构 */
  hasHtmlStructure: boolean;
  /** 优先级 */
  priority: TaskPriority;
  /** 在扫描结果中的位置 */
  position: number;
  /** 链接信息 */
  links?: Array<{ href: string; text: string }>;
  /** 元数据 */
  metadata?: {
    tagName: string;
    className?: string;
    textLength: number;
    isVisible: boolean;
  };
}

/**
 * 翻译结果
 */
export interface TranslationResult {
  /** 是否成功 */
  success: boolean;
  /** 原始文本 */
  originalText: string;
  /** 翻译后的文本 */
  translatedText?: string;
  /** 错误信息 */
  error?: string;
  /** 翻译格式 */
  format: TranslateFormat;
  /** 处理时长 */
  duration: number;
}

/**
 * 渲染结果
 */
export interface RenderResult {
  /** 是否成功 */
  success: boolean;
  /** 处理的元素 */
  element: HTMLElement;
  /** 使用的注入策略 */
  strategy?: InjectionStrategy;
  /** 错误信息 */
  error?: string;
  /** 渲染时长 */
  duration: number;
}

/**
 * 翻译状态
 */
export enum TranslationState {
  IDLE = 'idle',
  SCANNING = 'scanning',
  TRANSLATING = 'translating',
  RENDERING = 'rendering',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * 翻译事件类型
 */
export enum TranslationEventType {
  STATE_CHANGED = 'stateChanged',
  NODE_SCANNED = 'nodeScanned',
  TRANSLATION_COMPLETED = 'translationCompleted',
  RENDER_COMPLETED = 'renderCompleted',
  ERROR_OCCURRED = 'errorOccurred',
  PROGRESS_UPDATED = 'progressUpdated'
}

/**
 * 翻译事件数据
 */
export interface TranslationEvent<T = any> {
  /** 事件类型 */
  type: TranslationEventType;
  /** 事件数据 */
  data: T;
  /** 时间戳 */
  timestamp: number;
  /** 来源 */
  source: string;
}

/**
 * 状态变更事件数据
 */
export interface StateChangeEventData {
  /** 之前的状态 */
  previousState: TranslationState;
  /** 当前状态 */
  currentState: TranslationState;
  /** 变更原因 */
  reason?: string;
}

/**
 * 进度更新事件数据
 */
export interface ProgressEventData {
  /** 已处理数量 */
  processed: number;
  /** 总数量 */
  total: number;
  /** 成功数量 */
  successful: number;
  /** 失败数量 */
  failed: number;
  /** 当前处理的文本 */
  currentText?: string;
  /** 进度百分比 */
  percentage: number;
}

/**
 * 错误事件数据
 */
export interface ErrorEventData {
  /** 错误对象 */
  error: Error;
  /** 错误上下文 */
  context: string;
  /** 相关元素（如果有） */
  element?: HTMLElement;
  /** 是否为致命错误 */
  fatal: boolean;
}

/**
 * 翻译统计信息
 */
export interface TranslationStats {
  /** 扫描统计 */
  scan: {
    totalNodes: number;
    translatableNodes: number;
    processedNodes: number;
  };
  /** 翻译统计 */
  translation: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageTime: number;
  };
  /** 渲染统计 */
  render: {
    totalRenders: number;
    successfulRenders: number;
    failedRenders: number;
  };
}

/**
 * 翻译配置选项
 */
export interface TranslationConfig {
  /** 目标语言 */
  targetLanguage: string;
  /** 并发数量 */
  concurrency: number;
  /** 是否启用调试 */
  debug: boolean;
  /** 是否启用无障碍支持 */
  enableAccessibility: boolean;
  /** 是否启用懒加载 */
  enableLazyLoading: boolean;
  /** 是否启用性能优化 */
  enablePerformanceOptimization: boolean;
  /** 是否启用智能注入 */
  enableSmartInjection: boolean;
  /** 是否启用高级注入 */
  enableAdvancedInjection?: boolean;
  /** 翻译函数 */
  translateFunction?: (text: string, targetLang: string, format?: TranslateFormat) => Promise<string>;
  /** 错误处理回调 */
  onError?: (error: Error, context: string) => void;
  /** 进度回调 */
  onProgress?: (progress: ProgressEventData) => void;
}

/**
 * 生命周期任务
 */
export interface LifecycleTask {
  /** 任务ID */
  id: string;
  /** 任务类型 */
  type: 'translation' | 'render' | 'cleanup';
  /** 优先级 */
  priority: TaskPriority;
  /** 执行函数 */
  execute: () => Promise<any>;
  /** 任务数据 */
  data?: any;
}

/**
 * 扫描配置
 */
export interface ScanConfig {
  /** 根节点 */
  rootNode?: HTMLElement;
  /** 排除选择器 */
  excludeSelectors?: string[];
  /** 最小文本长度 */
  minTextLength?: number;
  /** 最大扫描深度 */
  maxDepth?: number;
  /** 异步分块大小，默认200 */
  chunkSize?: number;
  /** 视口扩展边距，默认200px */
  viewportMargin?: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
}

/**
 * 页面复杂度分析结果
 */
export interface PageComplexity {
  /** DOM节点总数 */
  domNodeCount: number;
  /** 是否包含无限滚动 */
  hasInfiniteScroll: boolean;
  /** 是否为动态内容页面 */
  isDynamic: boolean;
  /** 估算的复杂度等级 */
  estimatedComplexity: 'low' | 'medium' | 'high' | 'extreme';
}

/**
 * 视口边界信息
 */
export interface ViewportBounds {
  /** 顶部边界 */
  top: number;
  /** 底部边界 */
  bottom: number;
  /** 左侧边界 */
  left: number;
  /** 右侧边界 */
  right: number;
}

/**
 * 视口优先扫描结果
 */
export interface ViewportScanResult {
  /** 视口内的节点 */
  viewportNodes: ScannedNode[];
  /** 后台扫描Promise */
  backgroundScanPromise: Promise<ScannedNode[]>;
}

/**
 * 渲染配置
 */
export interface RenderConfig {
  /** 语言 */
  language: string;
  /** 是否启用无障碍 */
  enableAccessibility: boolean;
  /** 是否启用调试 */
  debug: boolean;
  /** 强制使用特定策略 */
  forceStrategy?: InjectionStrategy;
  /** 格式类型 */
  format?: 'text' | 'html';
}

/**
 * 编排器配置
 */
export interface OrchestratorConfig {
  /** 目标语言 */
  targetLanguage: string;
  /** 并发数量 */
  concurrency: number;
  /** 是否启用调试 */
  debug: boolean;
  /** 根节点 */
  rootNode?: HTMLElement;
  /** 翻译函数 */
  translateFunction?: (text: string, targetLang: string, format?: TranslateFormat) => Promise<string>;
  /** 视图控制器 */
  viewController?: ViewModeController;
  /** 是否启用无障碍支持 */
  enableAccessibility?: boolean;
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean;
  /** 是否启用性能优化 */
  enablePerformanceOptimization?: boolean;
  /** 是否启用智能注入 */
  enableSmartInjection?: boolean;
  /** 是否启用高级注入 */
  enableAdvancedInjection?: boolean;
  /** 错误处理回调 */
  onError?: (error: Error, context: string) => void;
  /** 进度回调 */
  onProgress?: (progress: any) => void;
}

/**
 * 批量翻译结果
 */
export interface BatchTranslationResult {
  /** 总数 */
  totalCount: number;
  /** 成功数 */
  successCount: number;
  /** 失败数 */
  failureCount: number;
  /** 结果列表 */
  results: TranslationResult[];
  /** 总耗时 */
  totalDuration: number;
}

/**
 * 视图模式
 */
export enum ViewMode {
  ORIGIN = 'origin',
  TRANSLATED = 'translated',
  DUAL = 'dual'
}

/**
 * 翻译事件总线
 */
export interface TranslationEventBus {
  /** 发送事件 */
  emit<T>(event: TranslationEventType, data: T): void;
  /** 监听事件 */
  on<T>(event: TranslationEventType, handler: (data: T) => void): void;
  /** 移除监听 */
  off<T>(event: TranslationEventType, handler: (data: T) => void): void;
  /** 销毁事件总线 */
  destroy(): void;
}

/**
 * 视图模式控制器
 */
export interface ViewModeController {
  /** 设置视图模式 */
  setViewMode(mode: ViewMode): void;
  /** 获取当前视图模式 */
  getCurrentMode(): ViewMode;
}