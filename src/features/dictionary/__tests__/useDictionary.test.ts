import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useDictionary } from '../useDictionary';
import { dictionaryService } from '../dictionary.service';
import { TooltipProps } from '@tooltip/index';

// Mock dictionary service
vi.mock('../dictionary.service', () => ({
  dictionaryService: {
    getWord: vi.fn(),
  },
}));

const mockDictionaryService = dictionaryService as any;

describe('useDictionary Hook', () => {
  const mockTooltipData: TooltipProps = {
    word: 'test',
    phonetic: {
      us: '/test/',
      uk: '/test/',
    },
    explain: [
      {
        pos: 'noun',
        definitions: [
          {
            definition: 'A procedure intended to establish quality',
            chinese: '测试，考验，试验的过程或方法',
            chinese_short: '测试',
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    // Setup default mock behavior
    mockDictionaryService.getWord.mockResolvedValue(mockTooltipData);
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  describe('基础功能', () => {
    it('应该返回正确的初始状态', () => {
      const { result } = renderHook(() => useDictionary(''));

      expect(result.current).toEqual({
        data: null,
        loading: false,
        error: null,
        isValidating: false,
        refetch: expect.any(Function),
        mutate: expect.any(Function),
      });
    });

    it('应该在传入单词时自动获取数据', async () => {
      const { result } = renderHook(() => useDictionary('test'));

      // 初始应该开始加载
      expect(result.current.loading).toBe(true);
      expect(result.current.data).toBe(null);
      expect(result.current.error).toBe(null);

      // 等待数据加载完成
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.data).toEqual(mockTooltipData);
      expect(result.current.error).toBe(null);
      expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
        fresh: false,
        skipCache: false,
        timeout: 3000, // 更新为实际的默认值
      });
    });

    it('应该在单词为空时不发起请求', () => {
      const { result } = renderHook(() => useDictionary(''));

      expect(result.current.loading).toBe(false);
      expect(result.current.data).toBe(null);
      expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
    });

    it('应该在单词变化时重新获取数据', async () => {
      const { result, rerender } = renderHook(
        ({ word }) => useDictionary(word),
        {
          initialProps: { word: 'test' },
        }
      );

      // 等待第一次请求完成
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // 更改单词
      rerender({ word: 'example' });

      expect(result.current.loading).toBe(true);
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledTimes(2);
      expect(mockDictionaryService.getWord).toHaveBeenLastCalledWith('example', {
        fresh: false,
        skipCache: false,
        timeout: 3000, // 更新为实际的默认值
      });
    });
  });

  describe('选项配置', () => {
    it('应该支持 enabled 选项', () => {
      const { result } = renderHook(() => 
        useDictionary('test', { enabled: false })
      );

      expect(result.current.loading).toBe(false);
      expect(result.current.data).toBe(null);
      expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
    });

    it('应该支持 fresh 选项', async () => {
      const { result } = renderHook(() => 
        useDictionary('test', { fresh: true })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
        fresh: true,
        skipCache: false,
        timeout: 3000, // 更新为实际的默认值
      });
    });

    it('应该支持 skipCache 选项', async () => {
      const { result } = renderHook(() => 
        useDictionary('test', { skipCache: true })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
        fresh: false,
        skipCache: true,
        timeout: 3000, // 更新为实际的默认值
      });
    });

    it('应该支持 timeout 选项', async () => {
      const { result } = renderHook(() => 
        useDictionary('test', { timeout: 5000 })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
        fresh: false,
        skipCache: false,
        timeout: 5000,
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理服务错误', async () => {
      const error = new Error('Service error');
      mockDictionaryService.getWord.mockRejectedValue(error);

      const { result } = renderHook(() => useDictionary('test'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toEqual(error);
      expect(result.current.data).toBe(null);
    });

    it('应该处理非 Error 类型的错误', async () => {
      mockDictionaryService.getWord.mockRejectedValue('String error');

      const { result } = renderHook(() => useDictionary('test'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBeInstanceOf(Error);
      expect(result.current.error?.message).toBe('String error');
    });

    it('应该支持重试逻辑', async () => {
      const mockError = new Error('First error');
      mockDictionaryService.getWord
        .mockRejectedValueOnce(mockError)
        .mockResolvedValue(mockTooltipData);

      const { result } = renderHook(() => 
        useDictionary('test', { retryCount: 1 })
      );

      // 等待重试完成
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      }, { timeout: 5000 });

      // 应该最终成功，因为第二次调用会成功
      expect(result.current.data).toEqual(mockTooltipData);
      expect(result.current.error).toBe(null);
      expect(mockDictionaryService.getWord).toHaveBeenCalledTimes(2);
    });

    it('应该在重试次数用完后显示错误', async () => {
      const error = new Error('Persistent error');
      mockDictionaryService.getWord.mockRejectedValue(error);

      const { result } = renderHook(() => 
        useDictionary('test', { retryCount: 1 })
      );

      // 等待所有重试完成
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      }, { timeout: 5000 });

      expect(result.current.error).toEqual(error);
      expect(result.current.data).toBe(null);
      expect(mockDictionaryService.getWord).toHaveBeenCalledTimes(2);
    });
  });

  describe('回调函数', () => {
    it('应该在成功时调用 onSuccess 回调', async () => {
      const onSuccess = vi.fn();
      
      const { result } = renderHook(() => 
        useDictionary('test', { onSuccess })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(onSuccess).toHaveBeenCalledWith(mockTooltipData);
    });

    it('应该在错误时调用 onError 回调', async () => {
      const onError = vi.fn();
      const error = new Error('Test error');
      mockDictionaryService.getWord.mockRejectedValue(error);

      const { result } = renderHook(() => 
        useDictionary('test', { onError })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(onError).toHaveBeenCalledWith(error);
    });

    it('应该在数据为 null 时不调用 onSuccess', async () => {
      const onSuccess = vi.fn();
      mockDictionaryService.getWord.mockResolvedValue(null);

      const { result } = renderHook(() => 
        useDictionary('test', { onSuccess })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('refetch 功能', () => {
    it('应该支持手动重新获取数据', async () => {
      const { result } = renderHook(() => useDictionary('test'));

      // 等待初始加载完成
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // 重新获取数据
      await act(async () => {
        await result.current.refetch();
      });

      // refetch之后isValidating应该变为false
      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledTimes(2);
    });

    it('应该在空单词时不执行 refetch', async () => {
      const { result } = renderHook(() => useDictionary(''));

      await act(async () => {
        await result.current.refetch();
      });

      expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
    });
  });

  describe('mutate 功能', () => {
    it('应该支持手动设置数据', async () => {
      const { result } = renderHook(() => useDictionary(''));

      const newData: TooltipProps = {
        word: 'manual',
        explain: [
          {
            pos: 'adjective',
            definitions: [
              {
                definition: 'Manual definition',
                chinese: '手动定义',
                chinese_short: '手动',
              },
            ],
          },
        ],
      };

      await act(async () => {
        result.current.mutate(newData);
      });

      expect(result.current.data).toEqual(newData);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.isValidating).toBe(false);
    });
  });

  describe('边界情况', () => {
    it('应该处理空白字符串', () => {
      const { result } = renderHook(() => useDictionary('   '));

      expect(result.current.loading).toBe(false);
      expect(result.current.data).toBe(null);
      expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
    });

    it('应该在组件卸载时清理资源', () => {
      const { unmount } = renderHook(() => useDictionary('test'));
      
      // 验证卸载不会抛出错误
      expect(() => unmount()).not.toThrow();
    });
  });
});