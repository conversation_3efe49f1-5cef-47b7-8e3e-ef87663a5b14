import { TooltipProps } from '@tooltip/index';
import { DictionaryStorage } from '@services/storage';
import { DictionaryApi } from './dictionary.api';
import { DictionaryApiConfig } from './types';

// 服务配置接口
export interface DictionaryServiceConfig {
  apiConfig?: DictionaryApiConfig;
  storageConfig?: {
    maxMemorySize?: number;
    expireHours?: number;
    keyPrefix?: string;
  };
}

// 获取选项
export interface GetWordOptions {
  fresh?: boolean;        // 强制刷新
  skipCache?: boolean;    // 跳过缓存
  timeout?: number;       // 请求超时
}

// 性能指标
export interface ServiceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  apiRequests: number;
  errorCount: number;
  averageResponseTime: number;
}

// 配置信息
export interface ServiceConfig {
  apiConfig: DictionaryApiConfig;
  storageConfig: {
    maxMemorySize: number;
    expireHours: number;
    keyPrefix: string;
  };
}

/**
 * 词典服务
 * 统一管理词典数据的获取、缓存和状态
 */
export class DictionaryService {
  private readonly storage: DictionaryStorage;
  private readonly api: DictionaryApi;
  private readonly pendingRequests = new Map<string, Promise<TooltipProps | null>>();
  private readonly metrics: ServiceMetrics;

  constructor(config: DictionaryServiceConfig = {}) {
    this.storage = new DictionaryStorage(config.storageConfig);
    this.api = new DictionaryApi(config.apiConfig);
    
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      apiRequests: 0,
      errorCount: 0,
      averageResponseTime: 0
    };
  }

  /**
   * 获取单词数据
   * @param word 单词
   * @param options 选项
   * @returns 词典数据
   */
  async getWord(word: string, options: GetWordOptions = {}): Promise<TooltipProps | null> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalRequests++;
      
      // 去重逻辑
      const cacheKey = this.getCacheKey(word, options);
      const existingRequest = this.pendingRequests.get(cacheKey);
      if (existingRequest) {
        return await existingRequest;
      }

      // 创建新请求
      const request = this.executeGetWord(word, options);
      this.pendingRequests.set(cacheKey, request);

      try {
        const result = await request;
        this.updateMetrics(startTime, result !== null);
        return result;
      } finally {
        this.pendingRequests.delete(cacheKey);
      }
    } catch (error) {
      this.metrics.errorCount++;
      this.updateMetrics(startTime, false);
      console.error(`Error getting word "${word}":`, error);
      return null;
    }
  }

  /**
   * 执行获取单词的核心逻辑
   * @param word 单词
   * @param options 选项
   * @returns 词典数据
   */
  private async executeGetWord(word: string, options: GetWordOptions): Promise<TooltipProps | null> {
    const { fresh = false, skipCache = false } = options;

    // 1. 检查缓存（除非强制刷新）
    if (!fresh && !skipCache) {
      try {
        const cached = await this.storage.get(word);
        if (cached) {
          this.metrics.cacheHits++;
          return cached;
        }
      } catch (error) {
        console.warn(`Cache read error for "${word}":`, error);
      }
    }

    // 2. 从API获取数据
    this.metrics.cacheMisses++;
    this.metrics.apiRequests++;
    
    const apiData = await this.api.getWord(word, { 
      fresh: fresh,
      timeout: options.timeout 
    });

    if (!apiData) {
      return null;
    }

    // 3. 保存到缓存（除非跳过缓存）
    if (!skipCache) {
      try {
        await this.storage.set(word, apiData);
      } catch (error) {
        console.warn(`Cache write error for "${word}":`, error);
        // 即使缓存失败，也返回数据
      }
    }

    return apiData;
  }

  /**
   * 批量获取单词
   * @param words 单词数组
   * @param options 选项
   * @returns 词典数据数组
   */
  async getWords(words: string[], options: GetWordOptions = {}): Promise<(TooltipProps | null)[]> {
    const promises = words.map(word => this.getWord(word, options));
    return await Promise.all(promises);
  }

  /**
   * 清理缓存
   */
  async clearCache(): Promise<void> {
    await this.storage.clear();
  }

  /**
   * 清理过期数据
   */
  async cleanExpiredData(): Promise<void> {
    await this.storage.cleanExpiredData();
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  async getCacheStats() {
    return await this.storage.getStats();
  }

  /**
   * 获取性能指标
   * @returns 性能指标
   */
  getMetrics(): ServiceMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics.totalRequests = 0;
    this.metrics.cacheHits = 0;
    this.metrics.cacheMisses = 0;
    this.metrics.apiRequests = 0;
    this.metrics.errorCount = 0;
    this.metrics.averageResponseTime = 0;
  }

  /**
   * 获取配置信息
   * @returns 配置信息
   */
  getConfig(): ServiceConfig {
    return {
      apiConfig: this.api.getConfig(),
      storageConfig: {
        maxMemorySize: 100,
        expireHours: 24,
        keyPrefix: 'dict_'
      }
    };
  }

  /**
   * 测试连接
   * @returns 是否连接成功
   */
  async testConnection(): Promise<boolean> {
    return await this.api.testConnection();
  }

  /**
   * 预热缓存
   * @param words 需要预热的单词列表
   */
  async warmupCache(words: string[]): Promise<void> {
    const chunks = this.chunkArray(words, 10); // 每次处理10个单词
    
    for (const chunk of chunks) {
      await this.getWords(chunk);
      // 添加小延迟避免过度请求
      await this.delay(100);
    }
  }

  /**
   * 获取词典服务状态
   * @returns 服务状态
   */
  async getServiceStatus() {
    const [cacheStats, isConnected] = await Promise.all([
      this.getCacheStats(),
      this.testConnection()
    ]);

    return {
      isConnected,
      cacheStats,
      metrics: this.getMetrics(),
      pendingRequests: this.pendingRequests.size
    };
  }

  // === 私有方法 ===

  /**
   * 生成缓存键
   * @param word 单词
   * @param options 选项
   * @returns 缓存键
   */
  private getCacheKey(word: string, options: GetWordOptions): string {
    const { fresh = false, skipCache = false } = options;
    return `${word}_${fresh}_${skipCache}`;
  }

  /**
   * 更新性能指标
   * @param startTime 开始时间
   * @param success 是否成功
   */
  private updateMetrics(startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    
    // 更新平均响应时间
    if (this.metrics.totalRequests > 0) {
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + duration) / 
        this.metrics.totalRequests;
    }
  }

  /**
   * 数组分块
   * @param array 数组
   * @param size 块大小
   * @returns 分块后的数组
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 延迟函数
   * @param ms 毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理挂起的请求
   */
  private clearPendingRequests(): void {
    this.pendingRequests.clear();
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.clearPendingRequests();
    this.resetMetrics();
  }
}

// 导出默认实例
export const dictionaryService = new DictionaryService();