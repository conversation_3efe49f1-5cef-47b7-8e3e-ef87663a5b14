/**
 * RegisterView Component - 注册界面组件
 * 与 LoginView 保持一致的设计风格，使用统一样式管理
 */

import React, { useState, useCallback } from 'react';
import { GoogleIcon, GitHubIcon, MailIcon, LockIcon, BackIcon } from '../icons';
import { useAuth } from '../../../hooks/useAuth';
import { useStyles } from '../../../styles';
import { validateRegisterForm } from '../../../utils/validation';

interface RegisterViewProps {
  onRegister: () => void;
  onShowLogin: () => void;
  onBack?: () => void;
}

export const RegisterView: React.FC<RegisterViewProps> = ({ 
  onRegister, 
  onShowLogin,
  onBack 
}) => {
  const { register, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [localError, setLocalError] = useState<string>('');
  const styles = useStyles();

  // 处理邮箱注册
  const handleEmailRegister = useCallback(async () => {
    // 清除之前的本地错误
    setLocalError('');

    // 使用验证工具进行表单验证
    const validation = validateRegisterForm(email, password, confirmPassword);
    
    if (validation.email && !validation.email.isValid) {
      setLocalError(validation.email.error || '邮箱格式不正确');
      return;
    }
    
    if (validation.password && !validation.password.isValid) {
      setLocalError(validation.password.error || '密码不符合要求');
      return;
    }
    
    if (validation.confirmPassword && !validation.confirmPassword.isValid) {
      setLocalError(validation.confirmPassword.error || '密码确认失败');
      return;
    }

    try {
      const result = await register(email.trim(), password);
      if (result.success) {
        onRegister(); // 通知父组件注册成功
      }
      // 错误消息会通过 useAuth hook 的 error 状态自动显示在界面上
    } catch (error) {
      console.error('[RegisterView] Register error:', error);
      // 不再使用 alert，错误会通过状态管理显示
    }
  }, [email, password, confirmPassword, register, onRegister]);

  // 处理社交注册（暂时显示提示）
  const handleSocialRegister = useCallback((provider: string) => {
    alert(`${provider} 注册功能即将上线`);
  }, []);

  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        {/* 返回按钮 */}
        {onBack && (
          <button
            onClick={onBack}
            style={styles.getInlineStyles('button', 'link')}
            className="lu-back-button"
          >
            <BackIcon />
            返回
          </button>
        )}

        {/* 标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={{
            ...styles.getInlineStyles('title'),
            fontWeight: '600'
          }}>
            创建账号
          </h1>
          <p style={styles.getInlineStyles('subtitle')}>
            注册 Lucid 账号，开启您的学习之旅
          </p>
        </div>

        {/* 错误提示 */}
        {(error || localError) && (
          <div 
            className="lu-error-message" 
            style={styles.getInlineStyles('errorMessage')}
          >
            {localError || error}
          </div>
        )}

        <div className="lu-social-login-group">
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialRegister('Google')}
            disabled={loading}
          >
            <GoogleIcon /> 使用 Google 注册
          </button>
          <button
            className="lu-social-login-btn github"
            onClick={() => handleSocialRegister('GitHub')}
            disabled={loading}
          >
            <GitHubIcon /> 使用 GitHub 注册
          </button>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="邮箱"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper">
            <LockIcon />
            <input
              type="password"
              placeholder="密码（至少6位）"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 确认密码输入框 */}
          <div className="lu-email-input-wrapper">
            <LockIcon />
            <input
              type="password"
              placeholder="确认密码"
              className="lu-email-input"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailRegister();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailRegister}
            disabled={loading}
          >
            {loading ? '注册中...' : '创建账号'}
          </button>

          {/* 底部链接 */}
          <div className="lu-register-links">
            <span style={styles.getInlineStyles('subtitle')}>
              已有账号？{' '}
              <button
                onClick={onShowLogin}
                style={{
                  ...styles.getInlineStyles('button', 'link'),
                  color: '#f97316'
                }}
              >
                立即登录
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
