/**
 * MyView Component - 1:1 复刻自设计文件
 * "我的" 页面，显示生词本和统计数据
 */

import React from 'react';
import { WordItem } from './WordItem';

// 从设计文件复制的示例数据
const wordData = [
  {
    id: 1,
    word: "escalade",
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "An act of scaling a wall or rampart, especially in military operations or as a sport, involving the use of ladders, ropes, or climbing techniques to overcome vertical obstacles and reach elevated positions safely",
            chinese: "攀登，攀爬墙壁或城墙的行为",
            chinese_short: "攀登",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition:
              "To climb or scale, especially a wall or fortification, as in a sport or military operation, or to climb a mountain, as in a mountaineering expedition, or to climb a ladder or rope using specialized equipment and techniques for safety and efficiency",
            chinese: "攀登，攀爬，特别是墙壁或防御工事",
            chinese_short: "攀登",
          },
        ],
      },
    ],
    wordFormats: [
      { name: "原型", form: "escalade" },
      { name: "第三人称单数", form: "escalades" },
      { name: "过去式", form: "escaladed" },
      { name: "过去分词", form: "escaladed" },
      { name: "现在分词", form: "escalading" },
    ],
    phonetic: { us: "/ˌɛskəˈleɪd/", uk: "/ˈɛskəleɪd/" },
  },
  {
    id: 2,
    word: "comprehensive",
    explain: [
      {
        pos: "adjective",
        definitions: [
          {
            definition:
              "Complete and including everything that is necessary; covering all or nearly all elements or aspects of something in a thorough and detailed manner, leaving no important parts uncovered or unexplored, and providing a full understanding of the subject matter through extensive analysis and examination",
            chinese: "全面的，综合的，包含所有必要元素的",
            chinese_short: "全面的",
          },
        ],
      },
    ],
    wordFormats: [
      { name: "原型", form: "comprehensive" },
      { name: "比较级", form: "more comprehensive" },
      { name: "最高级", form: "most comprehensive" },
    ],
    phonetic: { us: "/ˌkɑːmprɪˈhensɪv/", uk: "/ˌkɒmprɪˈhensɪv/" },
  },
  {
    id: 3,
    word: "favorite",
    explain: [
      {
        pos: "adjective",
        definitions: [
          {
            definition: "Preferred before all others of the same kind.",
            chinese: "最喜爱的",
            chinese_short: "最爱的",
          },
        ],
      },
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "A person or thing that is particularly popular or well-liked.",
            chinese: "特别受喜爱的人或物",
            chinese_short: "宠儿",
          },
        ],
      },
    ],
    wordFormats: [{ name: "原型", form: "favorite" }],
    phonetic: { us: "/ˈfeɪvərɪt/", uk: "/ˈfeɪvərɪt/" },
  },
];

export const MyView: React.FC = () => {
  return (
    <div id="view-my">
      <div className="lu-stats-group">
        <div className="lu-card lu-stat-card">
          <div className="lu-stat-number">784</div>
          <div className="lu-stat-title">生词本</div>
        </div>
        <div className="lu-card lu-stat-card">
          <div className="lu-stat-number">12</div>
          <div className="lu-stat-title">今日单词</div>
        </div>
      </div>
      
      <div id="word-list-container" className="lu-word-section">
        <div className="lu-section-header">
          <div className="lu-section-title">本页生词</div>
          <div className="lu-word-count">{wordData.length}</div>
        </div>
        {wordData.map((word) => (
          <WordItem key={word.id} data={word} />
        ))}
      </div>
    </div>
  );
};