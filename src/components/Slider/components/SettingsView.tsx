/**
 * SettingsView Component - 1:1 复刻自设计文件
 * 设置页面组件
 */

import React from 'react';
import { SettingItem } from './SettingItem';
import {
  UserIcon,
  VerifiedIcon,
  FeatherIcon,
  ImmersiveIcon,
  HoverTranslateIcon,
  AlertTriangleIcon,
  GlobeIcon,
  SpeakerIcon,
  ActivityIcon,
  MoonIcon,
  CommandIcon,
  HelpCircleIcon,
  MessageSquareIcon,
  RefreshCwIcon,
} from '../icons';

interface SettingsViewProps {
  onNavigate: (page: string) => void;
}

export const SettingsView: React.FC<SettingsViewProps> = ({ onNavigate }) => {
  return (
    <div id="view-settings">
      <div className="lu-settings-section">
        <div className="lu-card lu-settings-card">
          <div
            id="account-info-btn"
            className="lu-setting-item"
            onClick={() => onNavigate('account')}
          >
            <UserIcon className="lu-setting-icon" />
            <div className="lu-setting-title">账户信息</div>
            <div className="lu-user-info">
              <span className="lu-setting-value">
                R1ozron
                <VerifiedIcon className="lu-verified-icon" />
              </span>
            </div>
            <svg
              className="lu-setting-arrow"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </div>
        </div>
      </div>

      <div className="lu-settings-section">
        <div className="lu-settings-header">网页翻译设置</div>
        <div className="lu-card lu-settings-card">
          <SettingItem icon={<FeatherIcon />} title="翻译引擎" tag="NEW" />
          <SettingItem
            icon={<ImmersiveIcon />}
            title="沉浸翻译"
            value="对照翻译"
          />
          <SettingItem
            icon={<HoverTranslateIcon />}
            title="划词翻译"
            value="划词 + Alt"
          />
          <SettingItem
            icon={<AlertTriangleIcon />}
            title="学习语言"
            value="English"
          />
          <SettingItem icon={<GlobeIcon />} title="翻译语言" value="中文" />
        </div>
      </div>

      <div className="lu-settings-section">
        <div className="lu-settings-header">通用设置</div>
        <div className="lu-card lu-settings-card">
          <SettingItem icon={<SpeakerIcon />} title="自选发音" value="默认发音" />
          <SettingItem icon={<ActivityIcon />} title="界面语言" value="中文" />
          <SettingItem icon={<MoonIcon />} title="主题设置" value="Auto" />
          <SettingItem icon={<CommandIcon />} title="配置快捷键" />
        </div>
      </div>

      <div className="lu-settings-section">
        <div className="lu-card lu-settings-card">
          <SettingItem icon={<HelpCircleIcon />} title="视频教程" />
          <SettingItem icon={<MessageSquareIcon />} title="意见反馈" />
          <SettingItem icon={<RefreshCwIcon />} title="更新日志" value="0.0.1" />
        </div>
      </div>
    </div>
  );
};