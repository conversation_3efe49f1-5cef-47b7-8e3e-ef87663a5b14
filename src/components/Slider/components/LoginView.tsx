/**
 * LoginView Component - 1:1 复刻自设计文件
 * 登录界面组件 - 集成真实认证服务，使用统一样式管理
 */

import React, { useState, useCallback } from 'react';
import { GoogleIcon, GitHubIcon, MailIcon, LockIcon, BackIcon } from '../icons';
import { useAuth } from '../../../hooks/useAuth';
import { useStyles } from '../../../styles';
import { validateLoginForm } from '../../../utils/validation';

interface LoginViewProps {
  onLogin: () => void;
  onShowRegister: () => void;
  onBack?: () => void;
  onForgotPassword?: () => void;
}

export const LoginView: React.FC<LoginViewProps> = ({
  onLogin,
  onShowRegister,
  onBack,
  onForgotPassword
}) => {
  const { login, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [localError, setLocalError] = useState<string>('');
  const styles = useStyles();

  // 处理邮箱登录
  const handleEmailLogin = useCallback(async () => {
    // 清除之前的本地错误
    setLocalError('');

    // 使用验证工具进行表单验证
    const validation = validateLoginForm(email, password);
    
    if (validation.email && !validation.email.isValid) {
      setLocalError(validation.email.error || '邮箱格式不正确');
      return;
    }
    
    if (validation.password && !validation.password.isValid) {
      setLocalError(validation.password.error || '密码不能为空');
      return;
    }

    try {
      const result = await login(email.trim(), password);
      if (result.success) {
        onLogin(); // 通知父组件登录成功
      }
      // 错误消息会通过 useAuth hook 的 error 状态自动显示在界面上
    } catch (error) {
      console.error('[LoginView] Login error:', error);
      // 不再使用 alert，错误会通过状态管理显示
    }
  }, [email, password, login, onLogin]);



  // 处理社交登录（暂时显示提示）
  const handleSocialLogin = useCallback((provider: string) => {
    alert(`${provider} 登录功能即将上线`);
  }, []);

  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        {/* 返回按钮 */}
        {onBack && (
          <button
            onClick={onBack}
            style={styles.getInlineStyles('button', 'link')}
            className="lu-back-button"
          >
            <BackIcon />
            返回
          </button>
        )}

        {/* 标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={styles.getInlineStyles('title')}>
            欢迎回来
          </h1>
          <p style={styles.getInlineStyles('subtitle')}>
            请先登录账号，以便使用 Lucid 的完整功能
          </p>
        </div>

        {/* 错误提示 */}
        {(error || localError) && (
          <div 
            className="lu-error-message" 
            style={styles.getInlineStyles('errorMessage')}
          >
            {localError || error}
          </div>
        )}

        <div className="lu-social-login-group">
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialLogin('Google')}
            disabled={loading}
          >
            <GoogleIcon /> 继续使用 Google
          </button>
          <button
            className="lu-social-login-btn github"
            onClick={() => handleSocialLogin('GitHub')}
            disabled={loading}
          >
            <GitHubIcon /> 继续使用 GitHub
          </button>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="邮箱"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper">
            <LockIcon />
            <input
              type="password"
              placeholder="密码"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailLogin();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailLogin}
            disabled={loading}
          >
            {loading ? '登录中...' : '使用邮箱登录'}
          </button>

          {/* 底部链接 */}
          <div className="lu-login-links">
            <button
              onClick={onForgotPassword}
              style={styles.getInlineStyles('button', 'link')}
            >
              忘记密码？
            </button>
            <button
              onClick={onShowRegister}
              style={{
                ...styles.getInlineStyles('button', 'link'),
                color: '#f97316' // 使用品牌色
              }}
            >
              注册账号
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};