/**
 * Switch Component - 1:1 复刻自设计文件
 * 用于设置页面的开关控件
 */

import React, { useState } from 'react';

interface SwitchProps {
  defaultChecked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
}

export const Switch: React.FC<SwitchProps> = ({ 
  defaultChecked = false, 
  onChange,
  disabled = false 
}) => {
  const [isActive, setActive] = useState(defaultChecked);
  
  const handleClick = (e: React.MouseEvent) => {
    if (disabled) return;
    
    e.stopPropagation();
    const newValue = !isActive;
    setActive(newValue);
    onChange?.(newValue);
  };

  return (
    <div
      className={`lu-switch ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
      role="switch"
      aria-checked={isActive}
      aria-disabled={disabled}
      tabIndex={disabled ? -1 : 0}
      onClick={handleClick}
      onKeyDown={(e) => {
        if (disabled) return;
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick(e as any);
        }
      }}
    >
      <div className="lu-switch-thumb"></div>
    </div>
  );
};