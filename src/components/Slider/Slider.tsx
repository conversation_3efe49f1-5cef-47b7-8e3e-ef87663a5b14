/**
 * Slider Component - 1:1 复刻自 .claude/design/lu-silde/index.tsx
 * 主 Slider 组件，整合所有子组件 - 集成真实认证服务
 */

import React, { useState, useCallback } from 'react';
import { BackIcon, CloseIcon, MoreIcon, BookmarkIcon, SettingsIcon, BookIcon } from './icons';
import { LoginView } from './components/LoginView';
import { RegisterView } from './components/RegisterView';
import { MyView } from './components/MyView';
import { SettingsView } from './components/SettingsView';
import { LearnView } from './components/LearnView';
import { AccountView } from './components/AccountView';
import { useAuth } from '../../hooks/useAuth';

interface SliderProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

export const Slider: React.FC<SliderProps> = ({
  isOpen = true,
  onClose = () => {},
  className = ''
}) => {
  // 使用真实的认证状态
  const { isAuthenticated, user, logout } = useAuth();

  const [activeView, setActiveView] = useState<'my' | 'settings' | 'learn'>('my');
  const [currentPage, setCurrentPage] = useState<'main' | 'account'>('main');
  const [pageTitle, setPageTitle] = useState('Lucid');
  const [showRegister, setShowRegister] = useState(false);

  const handleLogin = useCallback(() => {
    // 登录成功后的处理逻辑
    // 认证状态由useAuth管理，这里可以添加额外的UI逻辑
    setShowRegister(false); // 确保回到登录视图
    console.log('[Slider] Login successful');
  }, []);

  const handleRegister = useCallback(() => {
    // 注册成功后的处理逻辑
    setShowRegister(false); // 注册成功后回到登录视图
    console.log('[Slider] Register successful');
  }, []);

  const handleShowRegister = useCallback(() => {
    setShowRegister(true);
  }, []);

  const handleShowLogin = useCallback(() => {
    setShowRegister(false);
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await logout();
      // 登出后重置UI状态
      setCurrentPage('main');
      setActiveView('my');
      console.log('[Slider] Logout successful');
    } catch (error) {
      console.error('[Slider] Logout error:', error);
    }
  }, [logout]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const changeView = useCallback((view: 'my' | 'settings' | 'learn') => {
    setActiveView(view);
    setCurrentPage('main');
    setPageTitle('Lucid');
  }, []);

  const navigateTo = useCallback((page: string) => {
    setCurrentPage(page as 'main' | 'account');
    if (page === 'account') {
      setPageTitle('Lucid 账号');
    }
  }, []);

  const goBack = useCallback(() => {
    setCurrentPage('main');
    setPageTitle('Lucid');
    // Ensure the correct footer button is active when going back
    if (currentPage === 'account') {
      setActiveView('settings');
    }
  }, [currentPage]);

  const renderView = () => {
    if (currentPage === 'account') {
      return <AccountView onLogout={handleLogout} user={user} />;
    }

    switch (activeView) {
      case 'settings':
        return <SettingsView onNavigate={navigateTo} />;
      case 'learn':
        return <LearnView />;
      case 'my':
      default:
        return <MyView />;
    }
  };

  // 如果未登录，显示登录或注册界面
  if (!isAuthenticated) {
    return (
      <div className={`lu-slide ${!isOpen ? 'hidden' : ''} ${className}`}>
        {showRegister ? (
          <RegisterView
            onRegister={handleRegister}
            onShowLogin={handleShowLogin}
          />
        ) : (
          <LoginView
            onLogin={handleLogin}
            onShowRegister={handleShowRegister}
            onForgotPassword={() => {
              alert('忘记密码功能即将上线');
            }}
          />
        )}
      </div>
    );
  }

  const isSubPage = currentPage !== 'main';

  return (
    <div className={`lu-slide ${!isOpen ? 'hidden' : ''} ${className}`}>
      {/* 头部 */}
      <div className={`lu-slider-header ${isSubPage ? 'is-page' : ''}`}>
        <button
          id="back-button"
          className="lu-header-button lu-back-button"
          aria-label="返回"
          onClick={goBack}
          style={{ display: isSubPage ? 'flex' : 'none' }}
        >
          <BackIcon />
        </button>

        <div
          className="lu-brand"
          style={{ display: isSubPage ? 'none' : 'flex' }}
        >
          <div className="lu-logo">L</div>
          <div className="lu-title">Lucid</div>
          <div className="lu-vip-tag early-bird">Early Bird</div>
        </div>
        
        {isSubPage && (
          <div id="header-title" className="lu-title">
            {pageTitle}
          </div>
        )}

        <button
          id="close-button"
          className="lu-header-button lu-close-button"
          aria-label="关闭"
          onClick={handleClose}
          style={{ display: isSubPage ? 'none' : 'flex' }}
        >
          <CloseIcon />
        </button>
        
        <button
          id="more-button"
          className="lu-header-button lu-more-button"
          aria-label="更多信息"
          style={{ display: isSubPage ? 'flex' : 'none' }}
        >
          <MoreIcon />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="lu-slider-content">
        {renderView()}
      </div>

      {/* 底部导航 */}
      <div className="lu-slider-footer">
        <button
          className={`lu-action-button ${
            activeView === 'my' && currentPage === 'main' ? 'active' : ''
          }`}
          onClick={() => changeView('my')}
        >
          <BookmarkIcon />
          <span>我的</span>
        </button>
        
        <button
          className={`lu-action-button ${
            activeView === 'settings' || currentPage === 'account' ? 'active' : ''
          }`}
          onClick={() => changeView('settings')}
        >
          <SettingsIcon />
          <span>设置</span>
        </button>
        
        <button
          className={`lu-action-button ${
            activeView === 'learn' && currentPage === 'main' ? 'active' : ''
          }`}
          onClick={() => changeView('learn')}
        >
          <BookIcon />
          <span>学习</span>
        </button>
      </div>
    </div>
  );
};