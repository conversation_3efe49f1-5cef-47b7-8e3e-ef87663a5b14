/**
 * DynamicTooltip 集成测试组件
 * 用于验证 useDictionary Hook 与 Tooltip 组件的集成
 */

import React, { useState } from 'react';
import { useDictionary } from '@dictionary/useDictionary';
import { Tooltip } from '@tooltip/index';
import { TooltipSkeleton } from './TooltipSkeleton';
import { TooltipError } from './TooltipError';

/**
 * 基础集成测试组件
 */
export const BasicTooltipIntegration: React.FC<{ word: string }> = ({ word }) => {
  const { data, loading, error, refetch } = useDictionary(word);

  console.log('🔄 Hook State:', { word, loading, error: !!error, data: !!data });

  if (loading) {
    return <TooltipSkeleton word={word} variant="compact" />;
  }

  if (error) {
    return (
      <TooltipError 
        error={error} 
        word={word} 
        onRetry={refetch}
        variant="standard"
      />
    );
  }

  if (data) {
    console.log('✅ Tooltip Data:', data);
    return (
      <Tooltip
        word={data.word}
        phonetic={data.phonetic}
        explain={data.explain}
        theme="dark"
      />
    );
  }

  return <span className="lu-tooltip-placeholder">No data for "{word}"</span>;
};

/**
 * 完整的集成测试页面
 */
export const TooltipIntegrationTest: React.FC = () => {
  const [testWord, setTestWord] = useState('hello');
  const [enableFresh, setEnableFresh] = useState(false);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>📝 Tooltip Hook 集成测试</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label>
          测试单词: 
          <input 
            type="text" 
            value={testWord}
            onChange={(e) => setTestWord(e.target.value)}
            style={{ marginLeft: '10px', padding: '5px' }}
          />
        </label>
        <label style={{ marginLeft: '20px' }}>
          <input 
            type="checkbox"
            checked={enableFresh}
            onChange={(e) => setEnableFresh(e.target.checked)}
          />
          强制刷新
        </label>
      </div>

      <div style={{ border: '1px solid #ccc', padding: '15px', marginBottom: '20px' }}>
        <h3>🎯 基础集成测试</h3>
        <BasicTooltipIntegration word={testWord} />
      </div>

      <div style={{ border: '1px solid #ccc', padding: '15px', marginBottom: '20px' }}>
        <h3>🔄 带选项的集成测试</h3>
        <TooltipWithOptions word={testWord} enableFresh={enableFresh} />
      </div>

      <div style={{ border: '1px solid #ccc', padding: '15px', marginBottom: '20px' }}>
        <h3>📊 多状态测试</h3>
        <MultiStateTest />
      </div>

      <div style={{ border: '1px solid #ccc', padding: '15px' }}>
        <h3>🧪 数据格式验证</h3>
        <DataFormatValidation word={testWord} />
      </div>
    </div>
  );
};

/**
 * 带选项的 Tooltip 测试
 */
const TooltipWithOptions: React.FC<{ word: string; enableFresh: boolean }> = ({ word, enableFresh }) => {
  const { data, loading, error, refetch, isValidating } = useDictionary(word, {
    fresh: enableFresh,
    timeout: 10000,
    retryCount: 2,
    onSuccess: (data) => console.log('✅ Success callback:', data),
    onError: (error) => console.log('❌ Error callback:', error),
  });

  const status = loading ? '加载中' : error ? '错误' : data ? '成功' : '无数据';
  const statusColor = loading ? 'orange' : error ? 'red' : data ? 'green' : 'gray';

  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        状态: <span style={{ color: statusColor, fontWeight: 'bold' }}>{status}</span>
        {isValidating && <span style={{ color: 'blue', marginLeft: '10px' }}>(验证中)</span>}
      </div>
      
      {loading && <TooltipSkeleton word={word} variant="compact" animated />}
      {error && (
        <TooltipError 
          error={error} 
          word={word} 
          onRetry={refetch}
          variant="detailed"
          showDetails
        />
      )}
      {data && (
        <Tooltip
          word={data.word}
          phonetic={data.phonetic}
          explain={data.explain}
          theme="dark"
        />
      )}
    </div>
  );
};

/**
 * 多状态测试组件
 */
const MultiStateTest: React.FC = () => {
  const words = ['hello', 'test', 'invalid-word-xyz'];
  
  return (
    <div>
      {words.map(word => (
        <div key={word} style={{ marginBottom: '15px', padding: '10px', border: '1px dotted #999' }}>
          <h4>"{word}"</h4>
          <BasicTooltipIntegration word={word} />
        </div>
      ))}
    </div>
  );
};

/**
 * 数据格式验证组件
 */
const DataFormatValidation: React.FC<{ word: string }> = ({ word }) => {
  const { data, loading, error } = useDictionary(word);

  if (loading) return <div>检查数据格式中...</div>;
  if (error) return <div>无法验证数据格式: {error.message}</div>;
  if (!data) return <div>无数据可验证</div>;

  // 验证数据格式
  const validations = [
    {
      name: 'word 字段',
      valid: typeof data.word === 'string' && data.word.length > 0,
      value: data.word
    },
    {
      name: 'phonetic 字段',
      valid: !data.phonetic || (typeof data.phonetic === 'object' && 
        (typeof data.phonetic.us === 'string' || typeof data.phonetic.uk === 'string')),
      value: data.phonetic ? `us: ${data.phonetic.us}, uk: ${data.phonetic.uk}` : 'undefined'
    },
    {
      name: 'explain 数组',
      valid: Array.isArray(data.explain) && data.explain.length > 0,
      value: `${data.explain?.length || 0} 个词性`
    },
    {
      name: 'explain 结构',
      valid: data.explain?.every(item => 
        typeof item.pos === 'string' &&
        Array.isArray(item.definitions) &&
        item.definitions.every(def => 
          typeof def.definition === 'string' &&
          typeof def.chinese === 'string' &&
          typeof def.chinese_short === 'string'
        )
      ) || false,
      value: 'pos, definitions 结构检查'
    }
  ];

  return (
    <div>
      <h4>📋 数据格式验证结果</h4>
      {validations.map((validation, index) => (
        <div key={index} style={{ marginBottom: '5px' }}>
          <span style={{ color: validation.valid ? 'green' : 'red' }}>
            {validation.valid ? '✅' : '❌'}
          </span>
          <strong>{validation.name}:</strong> {validation.value}
        </div>
      ))}
      
      <h4>🔍 完整数据结构</h4>
      <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
};

// 简单的测试运行器
export const runTooltipIntegrationTests = () => {
  console.log('🧪 开始 Tooltip Hook 集成测试...');
  
  // 可以在这里添加程序化测试
  const testResults = {
    hookCreation: '✅ Hook 创建成功',
    dataFetching: '⏳ 数据获取测试中...',
    errorHandling: '⏳ 错误处理测试中...',
    caching: '⏳ 缓存机制测试中...'
  };
  
  console.table(testResults);
  
  return testResults;
};

// 导出测试工具
export const TestUtils = {
  runIntegrationTests: runTooltipIntegrationTests,
  BasicTooltipIntegration,
  TooltipIntegrationTest,
};

// 全局测试函数
if (typeof window !== 'undefined') {
  (window as any).tooltipIntegrationTest = runTooltipIntegrationTests;
  (window as any).TooltipTestUtils = TestUtils;
}