/**
 * React包装器组件
 * 将纯JavaScript的VanillaTooltip包装成React组件
 * 提供与原Tooltip组件相同的API接口
 */

import React, { useRef, useEffect, useImperativeHandle, forwardRef } from "react";
import { VanillaTooltip, TooltipData, TooltipOptions } from "./VanillaTooltip";

export interface TooltipProps {
  /** 目标单词 */
  word: string;
  /** 音标信息 */
  phonetic?: {
    us?: string;
    uk?: string;
  };
  /** 解释数据 */
  explain?: Array<{
    pos: string; // 词性，如 "noun", "verb"
    definitions: Array<{
      definition: string;
      chinese: string;
      chinese_short: string;
    }>;
  }>;
  /** 额外的类名 */
  className?: string;
  /** 主题色彩，默认dark */
  theme?: "dark" | "light";
  /** 是否启用交互功能 */
  interactive?: boolean;
  /** 偏好更新回调 */
  onPreferenceUpdate?: () => void;
}

export interface TooltipRef {
  /** 获取当前动画状态 */
  isAnimating: () => boolean;
  /** 强制重新渲染 */
  forceUpdate: () => void;
  /** 销毁实例 */
  destroy: () => void;
}

export const TooltipWrapper = forwardRef<TooltipRef, TooltipProps>(({
  word,
  phonetic,
  explain = [],
  className = "",
  theme = "dark",
  interactive = false,
  onPreferenceUpdate,
}, ref) => {
  const containerRef = useRef<HTMLSpanElement>(null);
  const tooltipInstanceRef = useRef<VanillaTooltip | null>(null);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    isAnimating: () => tooltipInstanceRef.current?.isCurrentlyAnimating() || false,
    forceUpdate: () => {
      if (tooltipInstanceRef.current && containerRef.current) {
        const data: TooltipData = { word, phonetic, explain };
        tooltipInstanceRef.current.updateData(data);
      }
    },
    destroy: () => {
      tooltipInstanceRef.current?.destroy();
      tooltipInstanceRef.current = null;
    }
  }));

  // 初始化和更新VanillaTooltip实例
  useEffect(() => {
    if (!containerRef.current) return;

    const data: TooltipData = { word, phonetic, explain };
    const options: TooltipOptions = {
      theme,
      interactive,
      onPreferenceUpdate,
      className
    };

    // 如果实例已存在，更新数据和选项
    if (tooltipInstanceRef.current) {
      tooltipInstanceRef.current.updateData(data);
      tooltipInstanceRef.current.updateOptions(options);
    } else {
      // 创建新实例
      tooltipInstanceRef.current = new VanillaTooltip(
        containerRef.current,
        data,
        options
      );
    }

    // 清理函数
    return () => {
      // 注意：不在这里销毁实例，因为React可能会重新挂载组件
    };
  }, [word, phonetic, explain, theme, interactive, onPreferenceUpdate, className]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      tooltipInstanceRef.current?.destroy();
      tooltipInstanceRef.current = null;
    };
  }, []);

  return (
    <span 
      ref={containerRef}
      className="tooltip-wrapper"
      style={{ display: 'inline-block' }}
    />
  );
});

TooltipWrapper.displayName = 'TooltipWrapper';

// 默认导出，保持与原组件相同的导出方式
export const Tooltip = TooltipWrapper;

// 兼容性检查Hook
export const useTooltipCompatibility = () => {
  const isSupported = VanillaTooltip.isAnimationSupported();
  
  useEffect(() => {
    if (!isSupported) {
      console.warn('VanillaTooltip: FLIP animations not supported in this environment');
    }
  }, [isSupported]);

  return { isSupported };
};

// 性能监控Hook
export const useTooltipPerformance = (enabled = false) => {
  const performanceData = useRef<number[]>([]);
  
  const recordPerformance = (duration: number) => {
    if (!enabled) return;
    
    performanceData.current.push(duration);
    
    // 保持最近100次记录
    if (performanceData.current.length > 100) {
      performanceData.current.shift();
    }
  };

  const getStats = () => {
    const data = performanceData.current;
    if (data.length === 0) return null;

    const avg = data.reduce((a, b) => a + b, 0) / data.length;
    const min = Math.min(...data);
    const max = Math.max(...data);

    return { avg, min, max, count: data.length };
  };

  return { recordPerformance, getStats };
};
