/**
 * Tooltip 组件统一导出
 * 提供两种实现方式的选择
 */

// 原React版本（存在FLIP动画问题）
export { Tooltip as ReactTooltip } from './Tooltip';

// 新的纯JavaScript版本（推荐使用）
export { 
  Tooltip, 
  TooltipWrapper,
  useTooltipCompatibility,
  useTooltipPerformance 
} from './TooltipWrapper';

// 纯JavaScript实现类
export { VanillaTooltip } from './VanillaTooltip';

// 类型定义
export type { TooltipProps, TooltipRef } from './TooltipWrapper';
export type { TooltipData, TooltipOptions } from './VanillaTooltip';

// 默认导出新版本
export { Tooltip as default } from './TooltipWrapper';
