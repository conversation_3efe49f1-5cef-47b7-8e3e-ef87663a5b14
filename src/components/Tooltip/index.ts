/**
 * Tooltip 组件统一导出
 * 最新版本使用纯JavaScript实现，完美解决FLIP动画问题
 */

// 最新版本 - 纯JavaScript实现（推荐使用）
export {
  Tooltip,
  TooltipWrapper,
  useTooltipCompatibility,
  useTooltipPerformance
} from './TooltipWrapper';

// 纯JavaScript核心实现类
export { VanillaTooltip as TooltipCore } from './VanillaTooltip';

// 类型定义
export type { TooltipProps, TooltipRef } from './TooltipWrapper';
export type { TooltipData, TooltipOptions } from './VanillaTooltip';

// 默认导出最新版本
export { Tooltip as default } from './TooltipWrapper';

// ⚠️ 已废弃：React版本（存在FLIP动画瞬移问题）
/**
 * @deprecated 请使用新的 Tooltip 组件，该版本存在FLIP动画瞬移问题
 * @see https://github.com/your-repo/issues/flip-animation-issue
 */
export { Tooltip as ReactTooltip } from './Tooltip';
