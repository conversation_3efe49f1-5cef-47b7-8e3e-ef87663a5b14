/**
 * ⚠️ 已废弃的 React Tooltip 组件
 *
 * @deprecated 该版本存在FLIP动画瞬移问题，请使用新的 Tooltip 组件
 * @see 新版本: import { Tooltip } from '@tooltip/index'
 *
 * 问题描述：React重新渲染导致FLIP动画出现瞬移而非平滑过渡
 * 解决方案：使用基于纯JavaScript实现的新版本Tooltip组件
 *
 * 基于 .claude/design/tooltip.html 的设计实现
 * 使用固定类名，符合设计稿的 lu-tooltip 样式
 * 支持交互式偏好记录
 */

import React, { useState, useRef, useEffect } from "react";
import { recordPreference, sortByPreference } from "@dictionary/preference";
import "./interactive.css";

export interface TooltipProps {
  /** 目标单词 */
  word: string;
  /** 音标信息 */
  phonetic?: {
    us?: string;
    uk?: string;
  };
  /** 解释数据 */
  explain?: Array<{
    pos: string; // 词性，如 "noun", "verb"
    definitions: Array<{
      definition: string;
      chinese: string;
      chinese_short: string;
    }>;
  }>;
  /** 额外的类名 */
  className?: string;
  /** 主题色彩，默认dark */
  theme?: "dark" | "light";
  /** 是否启用交互功能 */
  interactive?: boolean;
  /** 偏好更新回调 */
  onPreferenceUpdate?: () => void;
}

export const Tooltip: React.FC<TooltipProps> = ({
  word,
  explain = [],
  className = "",
  theme = "dark",
  interactive = false,
  onPreferenceUpdate,
}) => {
  const [version, setVersion] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const containerRef = useRef<HTMLSpanElement>(null);
  const elementsRef = useRef<Map<string, HTMLElement>>(new Map());

  // 废弃警告
  useEffect(() => {
    console.warn(
      '⚠️ [DEPRECATED] 您正在使用已废弃的ReactTooltip组件，该版本存在FLIP动画瞬移问题。\n' +
      '请升级到新版本: import { Tooltip } from "@tooltip/index"\n' +
      '新版本提供完全相同的API，无需修改代码即可享受流畅的动画效果。'
    );
  }, []);

  // 如果启用交互功能，使用排序后的数据
  const sortedExplain = interactive ? sortByPreference(word, explain) : explain;

  // FLIP 动画：记录元素位置变化
  useEffect(() => {
    if (!interactive || !isAnimating) return;

    const elements = elementsRef.current;
    const container = containerRef.current;
    if (!container || elements.size === 0) return;

    // 获取所有元素的新位置
    const newPositions = new Map<string, DOMRect>();
    elements.forEach((element, key) => {
      if (element.parentElement) {
        newPositions.set(key, element.getBoundingClientRect());
      }
    });

    // 应用 FLIP 动画
    elements.forEach((element, key) => {
      const oldPos = element.dataset.oldPosition;
      const newPos = newPositions.get(key);

      if (oldPos && newPos) {
        const oldRect = JSON.parse(oldPos);
        const deltaX = oldRect.left - newPos.left;
        const deltaY = oldRect.top - newPos.top;

        if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
          // 设置初始位置（反转）
          element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
          element.style.transition = 'none';

          // 强制重排
          element.offsetHeight;

          // 播放动画到最终位置
          element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          element.style.transform = 'translate(0, 0)';
        }
      }

      // 清理
      delete element.dataset.oldPosition;
    });

    // 动画完成后重置状态
    const timer = setTimeout(() => {
      setIsAnimating(false);
      elements.forEach((element) => {
        element.style.transition = '';
        element.style.transform = '';
      });
    }, 300);

    return () => clearTimeout(timer);
  }, [version, interactive, isAnimating]);

  // 处理点击事件
  const handleDefinitionClick = (
    e: React.MouseEvent,
    pos: string,
    chineseShort: string
  ) => {
    if (!interactive || isAnimating) return;

    // 阻止事件冒泡，防止tooltip隐藏
    e.stopPropagation();
    e.preventDefault();

    console.log(`👆 Clicked: ${word} -> ${pos}.${chineseShort}`);

    // 记录当前所有元素的位置（FLIP 的 First 步骤）
    const elements = elementsRef.current;
    elements.forEach((element) => {
      if (element.parentElement) {
        const rect = element.getBoundingClientRect();
        element.dataset.oldPosition = JSON.stringify({
          left: rect.left,
          top: rect.top,
          width: rect.width,
          height: rect.height
        });
      }
    });

    // 添加点击反馈动画
    const clickedElement = e.currentTarget as HTMLElement;
    clickedElement.style.transform = 'scale(1.1)';
    clickedElement.style.transition = 'transform 0.1s ease-out';

    setTimeout(() => {
      clickedElement.style.transform = '';
      clickedElement.style.transition = '';
    }, 100);

    recordPreference(word, pos, chineseShort);
    setIsAnimating(true);
    setVersion((v) => v + 1); // 触发重新渲染

    console.log(`🔄 Version updated to trigger re-render with animation`);

    // 通知父组件更新数据
    if (onPreferenceUpdate) {
      onPreferenceUpdate();
    }
  };

  // 如果没有解释数据，使用默认结构
  let workingExplain = sortedExplain;
  if (workingExplain.length === 0) {
    workingExplain = [
      {
        pos: "noun",
        definitions: [
          {
            definition: "Example definition",
            chinese: "示例定义",
            chinese_short: "示例",
          },
          {
            definition: "Example definition2",
            chinese: "示例定义2",
            chinese_short: "示例2",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition: "Example definition3",
            chinese: "示例定义3",
            chinese_short: "示例3",
          },
        ],
      },
    ];
  }

  // 生成tooltip内容：pos1+short1+short2+' '+pos2+short1
  const generateTooltipContent = () => {
    const parts: React.ReactNode[] = [];

    workingExplain.forEach((item, index) => {
      // 添加词性简写
      const posShort =
        item.pos === "noun"
          ? "n."
          : item.pos === "verb"
          ? "v."
          : item.pos === "adjective"
          ? "adj."
          : item.pos === "adverb"
          ? "adv."
          : item.pos.substring(0, 3) + ".";

      parts.push(
        <span key={`pos-${index}`} className="lu-pos">
          {posShort}
        </span>
      );

      // 添加该词性下的所有中文简短释义
      item.definitions.forEach((def: any, defIndex: number) => {
        // 使用内容作为稳定的 key，确保动画正确
        const stableKey = `${item.pos}-${def.chinese_short}`;

        parts.push(
          <span
            key={stableKey}
            ref={(el) => {
              if (el && interactive) {
                elementsRef.current.set(stableKey, el);
              }
            }}
            className={[
              "lu-chinese-short",
              interactive && "interactive",
              isAnimating && "animating"
            ].filter(Boolean).join(" ")}
            onClick={(e) =>
              handleDefinitionClick(e, item.pos, def.chinese_short)
            }
            onMouseDown={(e) => interactive && e.stopPropagation()}
            onMouseUp={(e) => interactive && e.stopPropagation()}
            style={{
              cursor: interactive ? "pointer" : "default",
              userSelect: interactive ? "none" : "auto",
            }}
          >
            {def.chinese_short}
          </span>
        );
      });

      // 如果不是最后一个词性，添加空格分隔
      if (index < workingExplain.length - 1) {
        parts.push(
          <span key={`sep-${index}`} className="lu-separator">
            {" "}
          </span>
        );
      }
    });

    return parts;
  };

  return (
    <span
      ref={containerRef}
      className={[
        "lu-tooltip",
        interactive && "has-interactive",
        isAnimating && "animating",
        className
      ].filter(Boolean).join(" ")}
      data-theme={theme}
      // 防止事件冒泡到外部，确保tooltip内部点击不会关闭tooltip
      onClick={interactive ? (e) => e.stopPropagation() : undefined}
      onMouseDown={interactive ? (e) => e.stopPropagation() : undefined}
      onMouseUp={interactive ? (e) => e.stopPropagation() : undefined}
    >
      {generateTooltipContent()}
    </span>
  );
};

// 为了兼容不同的使用方式，也导出一个简单的工厂函数
export const createTooltip = (props: TooltipProps) => {
  return <Tooltip {...props} />;
};

// 预定义的常用词性
export const PART_OF_SPEECH = {
  NOUN: "n.",
  VERB: "v.",
  ADJECTIVE: "adj.",
  ADVERB: "adv.",
  PREPOSITION: "prep.",
  CONJUNCTION: "conj.",
  PRONOUN: "pron.",
  INTERJECTION: "interj.",
} as const;

// 示例数据，用于测试 - 使用新格式
export const TOOLTIP_EXAMPLES = {
  test: {
    word: "test",
    phonetic: {
      us: "/test/",
      uk: "/test/",
    },
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "A procedure intended to establish the quality, performance, or reliability of something",
            chinese: "测试，考验，试验的过程或方法",
            chinese_short: "测试",
          },
          {
            definition:
              "An examination of part of the body or a body fluid for medical purposes",
            chinese: "医学检查或化验",
            chinese_short: "检查",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition:
              "To take measures to check the quality, performance, or reliability of something",
            chinese: "测试，检验某物的质量或性能",
            chinese_short: "测试",
          },
        ],
      },
    ],
  },
  escalade: {
    word: "escalade",
    phonetic: {
      us: "/ˌɛskəˈleɪd/",
      uk: "/ˈɛskəleɪd/",
    },
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "An act of scaling a wall or rampart, especially in military operations or as a sport",
            chinese: "攀登，攀爬墙壁或城墙的行为",
            chinese_short: "攀登",
          },
          {
            definition:
              "A comprehensive approach involving all necessary elements",
            chinese: "全面，综合的，包含所有必要元素的",
            chinese_short: "全面",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition: "To climb or scale, especially a wall or fortification",
            chinese: "攀登，攀爬，特别是墙壁或防御工事",
            chinese_short: "攀登",
          },
        ],
      },
    ],
  },
  example: {
    word: "example",
    phonetic: {
      us: "/ɪɡˈzæmpəl/",
      uk: "/ɪɡˈzɑːmpəl/",
    },
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "A thing characteristic of its kind or illustrating a general rule",
            chinese: "例子，实例，范例",
            chinese_short: "例子",
          },
          {
            definition:
              "A person or thing regarded in terms of their fitness to be imitated",
            chinese: "榜样，模范",
            chinese_short: "榜样",
          },
        ],
      },
    ],
  },
} as const;
