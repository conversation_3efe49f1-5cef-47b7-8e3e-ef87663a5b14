/**
 * Tooltip 核心实现类
 * 基于纯JavaScript，完美解决React重新渲染导致的FLIP动画瞬移问题
 * 提供高性能、稳定的动画效果和完整的功能支持
 */

import { recordPreference, sortByPreference } from "@dictionary/preference";
import { styleManager } from "@styles/StyleManager";

export interface TooltipData {
  word: string;
  phonetic?: {
    us?: string;
    uk?: string;
  };
  explain?: Array<{
    pos: string;
    definitions: Array<{
      definition: string;
      chinese: string;
      chinese_short: string;
    }>;
  }>;
}

export interface TooltipOptions {
  theme?: "dark" | "light";
  interactive?: boolean;
  onPreferenceUpdate?: () => void;
  className?: string;
}

export class VanillaTooltip {
  private container: HTMLElement;
  private data: TooltipData;
  private options: TooltipOptions;
  private elementsMap = new Map<string, HTMLElement>();
  private isAnimating = false;
  private styleManager = styleManager;

  constructor(container: HTMLElement, data: TooltipData, options: TooltipOptions = {}) {
    this.container = container;
    this.data = data;
    this.options = {
      theme: "dark",
      interactive: false,
      ...options
    };
    // styleManager已经在类属性中初始化
    
    this.init();
  }

  private init(): void {
    // 确保样式已注入
    this.injectStyles();
    // 渲染初始内容
    this.render();
  }

  private injectStyles(): void {
    // 检查是否已经注入过样式
    if (document.getElementById('vanilla-tooltip-styles')) return;

    const styleElement = document.createElement('style');
    styleElement.id = 'vanilla-tooltip-styles';
    styleElement.textContent = this.styleManager.getComponentCSS('tooltip');
    document.head.appendChild(styleElement);
  }

  public updateData(newData: TooltipData): void {
    this.data = newData;
    this.render();
  }

  public updateOptions(newOptions: Partial<TooltipOptions>): void {
    this.options = { ...this.options, ...newOptions };
    this.render();
  }

  private render(): void {
    const { word, explain = [] } = this.data;
    const { interactive, theme } = this.options;

    // 清空容器
    this.container.innerHTML = '';
    this.elementsMap.clear();

    if (!explain.length) {
      this.container.innerHTML = `<span class="lu-tooltip" data-theme="${theme}">暂无释义</span>`;
      return;
    }

    // 获取排序后的数据
    const sortedExplain = interactive ? sortByPreference(word, explain) : explain;
    
    // 构建HTML
    const parts: string[] = [];
    
    sortedExplain.forEach((item, index) => {
      // 词性缩写
      const posShort = this.getPosShort(item.pos);
      parts.push(`<span class="lu-pos">${posShort}</span>`);
      
      // 中文释义
      item.definitions.forEach((def) => {
        const stableKey = `${item.pos}-${def.chinese_short}`;
        const interactiveClass = interactive ? 'interactive' : '';
        
        parts.push(`
          <span 
            class="lu-chinese-short ${interactiveClass}"
            data-key="${stableKey}"
            data-pos="${item.pos}"
            data-chinese="${def.chinese_short}"
          >
            ${def.chinese_short}
          </span>
        `);
      });
      
      // 分隔符
      if (index < sortedExplain.length - 1) {
        parts.push('<span class="lu-separator"> </span>');
      }
    });

    // 创建tooltip元素
    const tooltipHTML = `
      <span class="lu-tooltip ${this.options.className || ''}" data-theme="${theme}">
        ${parts.join('')}
      </span>
    `;

    this.container.innerHTML = tooltipHTML;

    // 绑定事件和建立元素引用
    if (interactive) {
      this.bindInteractiveEvents();
    }
  }

  private bindInteractiveEvents(): void {
    const interactiveElements = this.container.querySelectorAll('.lu-chinese-short.interactive');
    
    interactiveElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      const key = htmlElement.dataset.key!;
      const pos = htmlElement.dataset.pos!;
      const chinese = htmlElement.dataset.chinese!;
      
      // 建立元素引用
      this.elementsMap.set(key, htmlElement);
      
      // 绑定点击事件
      htmlElement.addEventListener('click', (e) => {
        this.handleDefinitionClick(e, pos, chinese);
      });
      
      // 阻止事件冒泡
      htmlElement.addEventListener('mousedown', (e) => e.stopPropagation());
      htmlElement.addEventListener('mouseup', (e) => e.stopPropagation());
    });
  }

  private handleDefinitionClick(
    event: Event,
    pos: string,
    chineseShort: string
  ): void {
    if (!this.options.interactive || this.isAnimating) return;

    event.stopPropagation();
    event.preventDefault();

    console.log(`👆 VanillaTooltip clicked: ${this.data.word} -> ${pos}.${chineseShort}`);

    // 记录当前所有元素的位置（FLIP 的 First 步骤）
    this.recordElementPositions();

    // 添加点击反馈动画
    this.addClickFeedback(event.currentTarget as HTMLElement);

    // 记录偏好
    recordPreference(this.data.word, pos, chineseShort);
    
    // 开始动画
    this.isAnimating = true;
    
    // 重新渲染
    this.render();
    
    // 执行FLIP动画
    requestAnimationFrame(() => {
      this.performFLIPAnimation();
    });

    // 触发回调
    if (this.options.onPreferenceUpdate) {
      this.options.onPreferenceUpdate();
    }
  }

  private recordElementPositions(): void {
    this.elementsMap.forEach((element) => {
      if (element.parentElement) {
        const rect = element.getBoundingClientRect();
        element.dataset.oldPosition = JSON.stringify({
          left: rect.left,
          top: rect.top,
          width: rect.width,
          height: rect.height
        });
      }
    });
  }

  private addClickFeedback(element: HTMLElement): void {
    element.style.transform = 'scale(1.1)';
    element.style.transition = 'transform 0.1s ease-out';
    
    setTimeout(() => {
      element.style.transform = '';
      element.style.transition = '';
    }, 100);
  }

  private performFLIPAnimation(): void {
    if (!this.isAnimating) return;

    // 获取所有元素的新位置
    const newPositions = new Map<string, DOMRect>();
    this.elementsMap.forEach((element, key) => {
      if (element.parentElement) {
        newPositions.set(key, element.getBoundingClientRect());
      }
    });

    // 应用 FLIP 动画
    this.elementsMap.forEach((element, key) => {
      const oldPos = element.dataset.oldPosition;
      const newPos = newPositions.get(key);

      if (oldPos && newPos) {
        const oldRect = JSON.parse(oldPos);
        const deltaX = oldRect.left - newPos.left;
        const deltaY = oldRect.top - newPos.top;

        if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
          // 设置初始位置（反转）
          element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
          element.style.transition = 'none';

          // 强制重排
          element.offsetHeight;

          // 播放动画到最终位置
          element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          element.style.transform = 'translate(0, 0)';
        }
      }

      // 清理位置数据
      delete element.dataset.oldPosition;
    });

    // 动画完成后重置状态
    setTimeout(() => {
      this.isAnimating = false;
      this.elementsMap.forEach((element) => {
        element.style.transition = '';
        element.style.transform = '';
      });
    }, 300);
  }

  private getPosShort(pos: string): string {
    const posMap: Record<string, string> = {
      'noun': 'n.',
      'verb': 'v.',
      'adjective': 'adj.',
      'adverb': 'adv.',
      'preposition': 'prep.',
      'conjunction': 'conj.',
      'interjection': 'int.',
      'pronoun': 'pron.',
      'determiner': 'det.',
      'modal': 'modal',
      'auxiliary': 'aux.'
    };
    
    return posMap[pos] || pos.substring(0, 3) + '.';
  }

  public destroy(): void {
    // 清理事件监听器
    this.elementsMap.forEach((element) => {
      element.replaceWith(element.cloneNode(true));
    });

    this.elementsMap.clear();
    this.container.innerHTML = '';
  }

  // 静态方法：检查是否支持FLIP动画
  public static isAnimationSupported(): boolean {
    return typeof requestAnimationFrame !== 'undefined' &&
           typeof DOMRect !== 'undefined';
  }

  // 获取当前动画状态
  public isCurrentlyAnimating(): boolean {
    return this.isAnimating;
  }
}
