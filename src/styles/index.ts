/**
 * 样式管理系统统一导出
 */

// 核心样式管理器
export { StyleManager, styleManager } from './StyleManager';

// 设计标记
export { colors, darkThemeColors, lightThemeColors, type ColorTokens } from './tokens/colors';
export { spacing, radius, sizes, type SpacingTokens, type RadiusTokens, type SizeTokens } from './tokens/spacing';
export { typography, transitions, type TypographyTokens, type TransitionTokens } from './tokens/typography';

// 导入样式管理器实例
import { styleManager } from './StyleManager';

// 工具函数
export const createStyleComposer = (manager: typeof styleManager) => {
  return {
    /**
     * 组合多个样式对象
     */
    compose: (...styleObjects: any[]) => {
      return Object.assign({}, ...styleObjects);
    },
    
    /**
     * 条件样式
     */
    conditional: (condition: boolean, trueStyles: any, falseStyles: any = {}) => {
      return condition ? trueStyles : falseStyles;
    },
    
    /**
     * 获取响应式样式
     */
    responsive: (styles: { mobile?: any; desktop?: any }) => {
      // 可以根据屏幕尺寸返回不同样式
      // 这里先返回桌面版样式
      return styles.desktop || styles.mobile || {};
    },
  };
};

// 便捷的样式访问
export const useStyles = () => styleManager;