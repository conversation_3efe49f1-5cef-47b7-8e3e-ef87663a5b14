/**
 * 统一翻译样式系统
 * 基于 StyleManager 的 Design Tokens 系统
 * 替代原有的 translate-tokens.css
 */

/* 导入 StyleManager 生成的 CSS 变量 */
@import './StyleManager.css';

/* 翻译系统的统一样式 */
.lu-translation-system {
  /* 使用 Design Tokens 的翻译样式 */
  font-size: var(--font-size-translation-base);
  line-height: var(--line-height-normal);
  transition: var(--transition-normal);
}

/* 原文样式 */
.lu-original-text {
  color: var(--color-translation-original);
  margin-bottom: var(--space-translation-tight);
  font-weight: var(--font-weight-normal);
}

/* 译文样式 */
.lu-translated-text {
  color: var(--color-translation-translated);
  opacity: 0.6;
  font-size: var(--font-size-translation-small);
  line-height: var(--line-height-tight);
}

/* 焦点状态 */
.lu-translation-focus {
  color: var(--color-translation-focus);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-radius: var(--radius-sm);
  outline: none;
}

/* 交互状态 */
.lu-translation-interactive:hover {
  background-color: var(--color-background-hover);
  border-radius: var(--radius-sm);
  padding: var(--space-xs);
  margin: calc(-1 * var(--space-xs));
}

/* 深色主题适配 */
html.dark .lu-original-text {
  color: var(--color-translation-original);
}

html.dark .lu-translated-text {
  color: var(--color-translation-translated);
}

html.dark .lu-translation-focus {
  color: var(--color-translation-focus);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lu-translation-system {
    font-size: 0.9em;
  }
  
  .lu-original-text {
    margin-bottom: 0.1rem;
  }
  
  .lu-translated-text {
    font-size: var(--font-size-xs);
  }
}

@media (min-width: 1200px) {
  .lu-translation-system {
    font-size: 1em;
    line-height: 1.7;
  }
}

/* 无障碍支持 */
@media (prefers-contrast: high) {
  .lu-translated-text {
    opacity: 1;
    color: var(--color-text-primary);
  }
}

@media (prefers-reduced-motion: reduce) {
  .lu-translation-system,
  .lu-translation-interactive {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .lu-translated-text {
    opacity: 0.8;
    color: #000000;
  }
  
  .lu-translation-focus {
    box-shadow: none;
    border: 1px solid #000000;
  }
}

/* 兼容性类名映射 */
.lu-origin-color { color: var(--color-translation-original); }
.lu-trans-color { color: var(--color-translation-translated); }
.lu-focus-color { color: var(--color-translation-focus); }

/* 间距工具类 */
.lu-spacing-xs { margin: var(--space-translation-tight); }
.lu-spacing-sm { margin: var(--space-translation-normal); }
.lu-spacing-md { margin: var(--space-translation-loose); }

/* 字体工具类 */
.lu-font-size-sm { font-size: var(--font-size-translation-small); }
.lu-font-size-base { font-size: var(--font-size-translation-base); }
.lu-font-size-lg { font-size: var(--font-size-translation-large); }
