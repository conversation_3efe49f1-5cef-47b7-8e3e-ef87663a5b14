/**
 * 间距设计标记
 * 统一的间距系统，包括内边距、外边距、间隙等
 */

export interface SpacingTokens {
  // 基础间距
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;

  // 翻译专用间距（更紧凑）
  translation: {
    tight: string;    // 翻译与原文的紧密间距
    normal: string;   // 正常间距
    loose: string;    // 宽松间距
  };
}

export interface RadiusTokens {
  // 圆角
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  full: string;
}

export interface SizeTokens {
  // 常用尺寸
  icon: {
    sm: string;
    md: string;
    lg: string;
  };
  button: {
    sm: string;
    md: string;
    lg: string;
  };
  input: {
    height: string;
  };
}

export interface ShadowTokens {
  // 阴影效果
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export const spacing: SpacingTokens = {
  xs: '4px',
  sm: '8px',
  md: '12px',
  lg: '16px',
  xl: '20px',
  '2xl': '24px',
  '3xl': '32px',
  '4xl': '40px',

  // 翻译专用间距
  translation: {
    tight: '0.125rem',   // 2px - 缩短翻译与原文的距离
    normal: '0.5rem',    // 8px - 正常间距
    loose: '0.75rem',    // 12px - 宽松间距
  },
};

export const radius: RadiusTokens = {
  none: '0',
  sm: '4px',
  md: '6px', 
  lg: '8px',
  xl: '12px',
  '2xl': '16px',
  full: '50%',
};

export const sizes: SizeTokens = {
  icon: {
    sm: '16px',
    md: '20px',
    lg: '24px',
  },
  button: {
    sm: '28px',
    md: '32px',
    lg: '40px',
  },
  input: {
    height: '44px',
  },
};

export const shadows: ShadowTokens = {
  sm: '0 2px 4px rgba(0, 0, 0, 0.1)',
  md: '0 4px 12px rgba(0, 0, 0, 0.15)',
  lg: '0 4px 32px rgba(0, 0, 0, 0.45)',
  xl: '0 8px 40px rgba(0, 0, 0, 0.6)',
};