/**
 * 字体设计标记
 * 字体族、字重、字号等定义
 */

export interface TypographyTokens {
  // 字体族
  fontFamily: {
    sans: string;
    mono: string;
  };
  
  // 字重
  fontWeight: {
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
  };
  
  // 字号
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;

    // 翻译专用字号
    translation: {
      small: string;    // 小字号
      base: string;     // 基础字号（相对于父元素）
      large: string;    // 大字号
    };
  };
  
  // 行高
  lineHeight: {
    none: string;
    tight: string;
    normal: string;
    relaxed: string;
  };
  
  // 字母间距
  letterSpacing: {
    tight: string;
    normal: string;
    wide: string;
  };
}

export interface TransitionTokens {
  // 动画过渡
  duration: {
    fast: string;
    normal: string;
    slow: string;
  };
  
  timing: {
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
  };
  
  // 预设过渡
  transition: {
    fast: string;
    normal: string;
    slow: string;
    all: string;
    colors: string;
    transform: string;
  };
}

export const typography: TypographyTokens = {
  fontFamily: {
    sans: '"Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
  },
  
  fontWeight: {
    light: '200',
    normal: '400', 
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  fontSize: {
    xs: '10px',
    sm: '12px',
    base: '14px',
    lg: '16px',
    xl: '18px',
    '2xl': '24px',
    '3xl': '32px',

    // 翻译专用字号
    translation: {
      small: '0.875rem',  // 14px
      base: '0.95em',     // 相对于父元素
      large: '1rem',      // 16px
    },
  },
  
  lineHeight: {
    none: '1',
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  
  letterSpacing: {
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
  },
};

export const transitions: TransitionTokens = {
  duration: {
    fast: '200ms',
    normal: '300ms', 
    slow: '350ms',
  },
  
  timing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out', 
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
  
  transition: {
    fast: '200ms cubic-bezier(0.4, 0, 0.2, 1)',
    normal: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: '350ms cubic-bezier(0.4, 0, 0.2, 1)',
    all: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    colors: 'background-color 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
};