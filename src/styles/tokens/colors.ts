/**
 * 颜色设计标记
 * 从现有样式中提取的颜色定义，支持主题切换
 */

export interface ColorTokens {
  // 品牌颜色
  brand: {
    primary: string;
    light: string;
    dark: string;
  };
  
  // 语义颜色  
  semantic: {
    success: string;
    danger: string;
    warning: string;
    info: string;
  };
  
  // 文字颜色
  text: {
    primary: string;
    secondary: string;
    muted: string;
    disabled: string;
    subtle: string;
    inverse: string;    // 反色文字（用于深色背景）
  };
  
  // 背景颜色
  background: {
    primary: string;
    secondary: string;
    hover: string;
    overlay: string;
    elevated: string;
    glass: string;      // 毛玻璃效果背景
    inverse: string;    // 反色背景（用于交互状态）
  };
  
  // 边框颜色
  border: {
    primary: string;
    secondary: string;
    hover: string;
    focus: string;
    glass: string;      // 毛玻璃效果边框
  };
  
  // 状态颜色
  status: {
    newTag: {
      background: string;
      text: string;
    };
    vip: {
      background: string;
      text: string;
      border: string;
    };
    earlyBird: {
      background: string;
      text: string;
      border: string;
    };
  };

  // 翻译相关颜色
  translation: {
    original: string;      // 原文颜色
    translated: string;    // 译文颜色
    focus: string;         // 焦点颜色
  };
}

export const darkThemeColors: ColorTokens = {
  brand: {
    primary: '#f97316',
    light: '#fb8b48', 
    dark: '#ea580c',
  },
  
  semantic: {
    success: '#22c55e',
    danger: '#ef4444',
    warning: '#facc15',
    info: '#3b82f6',
  },
  
  text: {
    primary: '#f5f5f5',
    secondary: '#e2e2e2',
    muted: '#c0c0c0',
    disabled: '#9e9e9e',
    subtle: '#757575',
    inverse: '#333333',     // 用于白色背景上的深色文字
  },
  
  background: {
    primary: 'rgba(45, 45, 48, 0.7)',
    secondary: 'rgba(30, 30, 32, 0.8)',
    hover: 'rgba(255, 255, 255, 0.1)',
    overlay: 'rgba(255, 255, 255, 0.05)',
    elevated: 'rgba(20, 20, 22, 0.7)',
    glass: 'rgba(30, 30, 30, 0.45)',      // Tooltip 毛玻璃背景
    inverse: '#ffffff',                    // 交互状态反色背景
  },
  
  border: {
    primary: 'rgba(255, 255, 255, 0.08)',
    secondary: 'rgba(255, 255, 255, 0.15)',
    hover: 'rgba(255, 255, 255, 0.15)',
    focus: 'rgba(249, 115, 22, 0.4)',
    glass: 'rgba(255, 255, 255, 0.15)',    // Tooltip 毛玻璃边框
  },
  
  status: {
    newTag: {
      background: 'rgba(59, 130, 246, 0.2)',
      text: '#3b82f6',
    },
    vip: {
      background: 'rgba(250, 204, 21, 0.15)',
      text: '#facd15',
      border: 'rgba(250, 204, 21, 0.2)',
    },
    earlyBird: {
      background: 'rgba(129, 140, 248, 0.1)',
      text: '#818cf8',
      border: 'rgba(129, 140, 248, 0.4)',
    },
  },

  translation: {
    original: '#111827',    // 原文颜色（深色）
    translated: '#6b7280',  // 译文颜色（灰色）
    focus: '#3b82f6',       // 焦点颜色（蓝色）
  },
};

// 预留亮色主题支持
export const lightThemeColors: ColorTokens = {
  // TODO: 实现亮色主题颜色定义
  ...darkThemeColors, // 暂时使用暗色主题

  // 浅色主题的翻译颜色
  translation: {
    original: '#f9fafb',   // 原文颜色（浅色）
    translated: '#9ca3af', // 译文颜色（浅灰）
    focus: '#60a5fa',      // 焦点颜色（浅蓝）
  },
};

export const colors = darkThemeColors;