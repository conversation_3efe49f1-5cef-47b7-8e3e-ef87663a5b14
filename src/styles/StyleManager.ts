import type { CSSProperties } from 'react';
import { colors, type ColorTokens } from './tokens/colors';
import { spacing, radius, sizes, shadows, type SpacingTokens, type RadiusTokens, type SizeTokens, type ShadowTokens } from './tokens/spacing';
import { typography, transitions, type TypographyTokens, type TransitionTokens } from './tokens/typography';

/**
 * 样式管理器
 * 统一管理所有样式，支持CSS变量、主题切换和Shadow DOM注入
 */
export class StyleManager {
  private readonly tokens: {
    colors: ColorTokens;
    spacing: SpacingTokens;
    radius: RadiusTokens;
    sizes: SizeTokens;
    shadows: ShadowTokens;
    typography: TypographyTokens;
    transitions: TransitionTokens;
  };

  private cssVariablesCache: string | null = null;
  private componentStylesCache: Map<string, string> = new Map();

  constructor() {
    this.tokens = {
      colors,
      spacing,
      radius,
      sizes,
      shadows,
      typography,
      transitions,
    };
  }

  /**
   * 生成CSS变量字符串
   */
  generateCSSVariables(): string {
    if (this.cssVariablesCache) {
      return this.cssVariablesCache;
    }

    const variables: string[] = [
      '/* Design Tokens as CSS Variables */',
      ':host, :root {',
    ];

    // 颜色变量
    variables.push('  /* Colors */');
    this.generateColorVariables(variables, 'brand', this.tokens.colors.brand);
    this.generateColorVariables(variables, 'semantic', this.tokens.colors.semantic);
    this.generateColorVariables(variables, 'text', this.tokens.colors.text);
    this.generateColorVariables(variables, 'background', this.tokens.colors.background);
    this.generateColorVariables(variables, 'border', this.tokens.colors.border);
    this.generateColorVariables(variables, 'translation', this.tokens.colors.translation);

    // 间距变量
    variables.push('  /* Spacing */');
    Object.entries(this.tokens.spacing).forEach(([key, value]) => {
      if (typeof value === 'object') {
        // 处理嵌套对象（如 translation）
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables.push(`  --space-${key}-${subKey}: ${subValue};`);
        });
      } else {
        variables.push(`  --space-${key}: ${value};`);
      }
    });

    // 圆角变量  
    variables.push('  /* Radius */');
    Object.entries(this.tokens.radius).forEach(([key, value]) => {
      variables.push(`  --radius-${key}: ${value};`);
    });

    // 字体变量
    variables.push('  /* Typography */');
    Object.entries(this.tokens.typography.fontFamily).forEach(([key, value]) => {
      variables.push(`  --font-${key}: ${value};`);
    });
    Object.entries(this.tokens.typography.fontWeight).forEach(([key, value]) => {
      variables.push(`  --font-weight-${key}: ${value};`);
    });
    Object.entries(this.tokens.typography.fontSize).forEach(([key, value]) => {
      if (typeof value === 'object') {
        // 处理嵌套对象（如 translation）
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables.push(`  --font-size-${key}-${subKey}: ${subValue};`);
        });
      } else {
        variables.push(`  --font-size-${key}: ${value};`);
      }
    });
    Object.entries(this.tokens.typography.lineHeight).forEach(([key, value]) => {
      variables.push(`  --line-height-${key}: ${value};`);
    });

    // 阴影变量
    variables.push('  /* Shadows */');
    Object.entries(this.tokens.shadows).forEach(([key, value]) => {
      variables.push(`  --shadow-${key}: ${value};`);
    });

    // 过渡变量
    variables.push('  /* Transitions */');
    Object.entries(this.tokens.transitions.transition).forEach(([key, value]) => {
      variables.push(`  --transition-${key}: ${value};`);
    });

    variables.push('}');
    
    this.cssVariablesCache = variables.join('\n');
    return this.cssVariablesCache;
  }

  /**
   * 生成颜色CSS变量
   */
  private generateColorVariables(variables: string[], prefix: string, colorObj: any): void {
    Object.entries(colorObj).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables.push(`  --color-${prefix}-${key}: ${value};`);
      } else if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables.push(`  --color-${prefix}-${key}-${subKey}: ${subValue};`);
        });
      }
    });
  }

  /**
   * 获取内联样式对象
   */
  getInlineStyles(componentName: string, variant?: string): CSSProperties {
    const key = variant ? `${componentName}-${variant}` : componentName;
    
    // 根据组件名返回相应的样式对象
    switch (componentName) {
      case 'title':
        return {
          color: this.tokens.colors.text.primary,
          fontSize: this.tokens.typography.fontSize['2xl'],
          fontWeight: this.tokens.typography.fontWeight.light,
          margin: '0 0 8px 0',
          fontFamily: this.tokens.typography.fontFamily.sans,
        };
        
      case 'subtitle': 
        return {
          color: this.tokens.colors.text.disabled,
          fontSize: this.tokens.typography.fontSize.base,
          margin: '0',
          lineHeight: this.tokens.typography.lineHeight.normal,
          fontWeight: this.tokens.typography.fontWeight.light,
          fontFamily: this.tokens.typography.fontFamily.sans,
        };
        
      case 'errorMessage':
        return {
          color: this.tokens.colors.semantic.danger,
          textAlign: 'center' as const,
          marginBottom: this.tokens.spacing.lg,
          fontSize: this.tokens.typography.fontSize.base,
          fontWeight: this.tokens.typography.fontWeight.light,
          fontFamily: this.tokens.typography.fontFamily.sans,
        };
        
      case 'button':
        return this.getButtonStyles(variant);
        
      case 'input':
        return this.getInputStyles(variant);
        
      default:
        return {};
    }
  }

  /**
   * 获取按钮样式
   */
  private getButtonStyles(variant?: string): CSSProperties {
    const baseStyles: CSSProperties = {
      border: 'none',
      borderRadius: this.tokens.radius.lg,
      cursor: 'pointer',
      fontSize: this.tokens.typography.fontSize.base,
      fontWeight: this.tokens.typography.fontWeight.medium,
      transition: this.tokens.transitions.transition.all,
      fontFamily: this.tokens.typography.fontFamily.sans,
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyles,
          backgroundColor: this.tokens.colors.brand.primary,
          color: '#ffffff',
          padding: `${this.tokens.spacing.md} ${this.tokens.spacing.lg}`,
        };
        
      case 'social':
        return {
          ...baseStyles,
          width: '100%',
          padding: `10px ${this.tokens.spacing.lg}`,
          border: `1px solid ${this.tokens.colors.border.primary}`,
          backgroundColor: this.tokens.colors.background.primary,
          color: this.tokens.colors.text.primary,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: this.tokens.spacing.md,
        };
        
      case 'link':
        return {
          ...baseStyles,
          background: 'none',
          color: this.tokens.colors.text.disabled,
          textDecoration: 'none',
          padding: 0,
        };
        
      default:
        return baseStyles;
    }
  }

  /**
   * 获取输入框样式  
   */
  private getInputStyles(variant?: string): CSSProperties {
    const baseStyles: CSSProperties = {
      width: '100%',
      boxSizing: 'border-box' as const,
      backgroundColor: this.tokens.colors.background.elevated,
      border: `1px solid ${this.tokens.colors.border.primary}`,
      borderRadius: this.tokens.radius.lg,
      color: this.tokens.colors.text.primary,
      fontSize: this.tokens.typography.fontSize.base,
      outline: 'none',
      transition: this.tokens.transitions.transition.all,
      fontFamily: this.tokens.typography.fontFamily.sans,
    };

    switch (variant) {
      case 'withIcon':
        return {
          ...baseStyles,
          padding: `${this.tokens.spacing.md} ${this.tokens.spacing.lg} ${this.tokens.spacing.md} 44px`,
        };
        
      default:
        return {
          ...baseStyles,
          padding: `${this.tokens.spacing.md} ${this.tokens.spacing.lg}`,
        };
    }
  }

  /**
   * 获取组件CSS类名（用于生成CSS字符串）
   */
  getComponentCSS(componentName: string): string {
    if (this.componentStylesCache.has(componentName)) {
      return this.componentStylesCache.get(componentName)!;
    }

    let css = '';

    switch (componentName) {
      case 'container':
        css = this.generateContainerCSS();
        break;
      case 'loginBox':
        css = this.generateLoginBoxCSS();
        break;
      case 'socialButton':
        css = this.generateSocialButtonCSS();
        break;
      case 'input':
        css = this.generateInputCSS();
        break;
      case 'tooltip':
        css = this.generateTooltipCSS();
        break;
      case 'translation':
        css = this.generateTranslationCSS();
        break;
      default:
        css = '';
    }

    this.componentStylesCache.set(componentName, css);
    return css;
  }

  /**
   * 生成完整的CSS字符串用于Shadow DOM注入
   */
  getCompleteCSS(): string {
    const parts = [
      '/* Font Import */',
      '@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap");',
      '',
      this.generateCSSVariables(),
      '',
      '/* Component Styles */',
      this.getComponentCSS('container'),
      this.getComponentCSS('loginBox'),
      this.getComponentCSS('socialButton'),
      this.getComponentCSS('input'),
      this.getComponentCSS('tooltip'),
      this.generateSliderCSS(),
    ];

    return parts.join('\n');
  }

  /**
   * 生成专门用于Shadow DOM的CSS（支持主题）
   */
  getShadowDOMCSS(components: string[] = [], theme: 'dark' | 'light' = 'dark'): string {
    const parts = [
      '/* Font Import */',
      '@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap");',
      '',
      this.generateCSSVariables(),
      '',
      '/* Component Styles */',
      ...components.map(comp => this.getComponentCSS(comp)),
    ];

    return parts.join('\n');
  }

  /**
   * 生成容器CSS
   */
  private generateContainerCSS(): string {
    return `
.lu-login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${this.tokens.spacing.xl};
  box-sizing: border-box;
}`;
  }

  /**
   * 生成登录框CSS
   */
  private generateLoginBoxCSS(): string {
    return `
.lu-login-box {
  width: 100%;
  max-width: 360px;
  background: ${this.tokens.colors.background.secondary};
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid ${this.tokens.colors.border.primary};
  border-radius: ${this.tokens.radius['2xl']};
  padding: ${this.tokens.spacing['3xl']};
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5);
}`;
  }

  /**
   * 生成社交按钮CSS
   */
  private generateSocialButtonCSS(): string {
    return `
.lu-social-login-group {
  display: flex;
  flex-direction: column;
  gap: ${this.tokens.spacing.md};
}

.lu-social-login-btn {
  width: 100%;
  padding: 10px ${this.tokens.spacing.lg};
  border-radius: ${this.tokens.radius.lg};
  border: 1px solid ${this.tokens.colors.border.primary};
  background-color: ${this.tokens.colors.background.primary};
  color: ${this.tokens.colors.text.primary};
  font-size: ${this.tokens.typography.fontSize.base};
  font-weight: ${this.tokens.typography.fontWeight.medium};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${this.tokens.spacing.md};
  transition: ${this.tokens.transitions.transition.all};
  font-family: ${this.tokens.typography.fontFamily.sans};
}

.lu-social-login-btn:hover {
  border-color: ${this.tokens.colors.border.secondary};
}

.lu-social-login-btn svg {
  width: ${this.tokens.sizes.icon.md};
  height: ${this.tokens.sizes.icon.md};
}

.lu-social-login-btn.google,
.lu-social-login-btn.github {
  color: #fff;
  background-color: #333;
}

.lu-social-login-btn.google:hover,
.lu-social-login-btn.github:hover {
  background-color: #444;
}`;
  }

  /**
   * 生成输入框CSS
   */
  private generateInputCSS(): string {
    return `
.lu-email-form {
  display: flex;
  flex-direction: column;
  margin-top: ${this.tokens.spacing.md};
}

.lu-email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: ${this.tokens.spacing.md};
}

.lu-email-input-wrapper:first-child {
  margin-top: 0;
}

.lu-email-input-wrapper svg {
  position: absolute;
  left: 14px;
  color: ${this.tokens.colors.text.subtle};
}

.lu-email-input {
  width: 100%;
  box-sizing: border-box;
  background-color: ${this.tokens.colors.background.elevated};
  border: 1px solid ${this.tokens.colors.border.primary};
  border-radius: ${this.tokens.radius.lg};
  padding: ${this.tokens.spacing.md} ${this.tokens.spacing.lg} ${this.tokens.spacing.md} 44px;
  color: ${this.tokens.colors.text.primary};
  font-size: ${this.tokens.typography.fontSize.base};
  outline: none;
  transition: ${this.tokens.transitions.transition.all};
  font-family: ${this.tokens.typography.fontFamily.sans};
}

.lu-email-input::placeholder {
  color: ${this.tokens.colors.text.subtle};
}

.lu-email-input:focus {
  border-color: ${this.tokens.colors.brand.primary};
  box-shadow: 0 0 0 3px ${this.tokens.colors.border.focus};
}

.lu-send-code-btn {
  width: 100%;
  padding: ${this.tokens.spacing.md};
  border: none;
  background-color: ${this.tokens.colors.brand.primary};
  color: white;
  font-size: ${this.tokens.typography.fontSize.base};
  font-weight: ${this.tokens.typography.fontWeight.semibold};
  border-radius: ${this.tokens.radius.lg};
  cursor: pointer;
  transition: ${this.tokens.transitions.transition.colors};
  margin-top: ${this.tokens.spacing.lg};
  font-family: ${this.tokens.typography.fontFamily.sans};
}

.lu-send-code-btn:hover {
  background-color: ${this.tokens.colors.brand.light};
}

.lu-send-code-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}`;
  }

  /**
   * 生成Slider组件的CSS（基于Design Tokens，与slider-styles.ts保持一致）
   */
  private generateSliderCSS(): string {
    // 注意：这个方法主要用于getCompleteCSS()的向后兼容
    // 实际的Slider样式应该使用slider-styles.ts中的SliderStyleGenerator
    return `
/* Slider Base Styles - 基于Design Tokens */
.lu-slide {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  z-index: 2147483647;
  backdrop-filter: blur(28px) saturate(150%);
  -webkit-backdrop-filter: blur(28px) saturate(150%);
  background: rgba(28, 28, 30, 0.92);
  border-left: 1px solid;
  border-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
  transition: var(--transition-all);
  transform: translateX(0);
  opacity: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--font-sans);
  font-weight: var(--font-weight-normal);
}

.lu-slide.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

@media (max-width: 767px) {
  .lu-slide {
    width: 100vw;
    border-radius: 0;
    border-left: none;
  }
}`;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cssVariablesCache = null;
    this.componentStylesCache.clear();
  }

  /**
   * 生成翻译系统的 CSS
   */
  private generateTranslationCSS(): string {
    return `
      /* 翻译系统样式 */
      .lu-translation-container {
        font-size: var(--font-size-translation-base);
        line-height: var(--line-height-normal);
        transition: var(--transition-normal);
      }

      .lu-original-text {
        color: var(--color-translation-original);
        margin-bottom: var(--space-translation-tight);
      }

      .lu-translated-text {
        color: var(--color-translation-translated);
        opacity: 0.6;
        font-size: var(--font-size-translation-small);
        line-height: var(--line-height-tight);
      }

      .lu-translation-focus {
        color: var(--color-translation-focus);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        border-radius: var(--radius-sm);
      }

      /* 响应式适配 */
      @media (max-width: 768px) {
        .lu-translation-container {
          font-size: 0.9em;
        }
        .lu-original-text {
          margin-bottom: 0.1rem;
        }
      }

      @media (min-width: 1200px) {
        .lu-translation-container {
          font-size: 1em;
          line-height: 1.7;
        }
      }

      /* 高对比度支持 */
      @media (prefers-contrast: high) {
        .lu-translated-text {
          opacity: 1;
          color: var(--color-text-primary);
        }
      }

      /* 减动画支持 */
      @media (prefers-reduced-motion: reduce) {
        .lu-translation-container {
          transition: none;
        }
      }

      /* 打印样式 */
      @media print {
        .lu-translated-text {
          opacity: 0.8;
          color: #000000;
        }
      }
    `;
  }

  /**
   * 生成Tooltip组件CSS
   */
  private generateTooltipCSS(): string {
    return `
/* Tooltip 组件样式 - 使用 Design Tokens */
.lu-tooltip {
  /* 毛玻璃效果 */
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);

  /* 背景和边框 - 使用 Design Tokens */
  background: var(--color-background-glass);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--radius-lg);

  /* 布局和间距 */
  padding: var(--space-xs) var(--space-md);
  display: inline-block;
  white-space: nowrap;

  /* 文本样式 */
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
  font-family: var(--font-sans);

  /* 动画效果 */
  transition: var(--transition-all);

  /* 阴影效果 */
  box-shadow: var(--shadow-lg);

  /* 防止文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 词性样式 */
.lu-pos {
  font-weight: var(--font-weight-medium);
  color: inherit;
  margin-right: var(--space-xs);
}

/* 中文简短释义样式 */
.lu-chinese-short {
  font-size: var(--font-size-sm);
  color: inherit;
  margin-right: var(--space-xs);
}

.lu-chinese-short:hover {
  color: var(--color-text-primary) !important;
}

/* 交互式中文释义的特殊hover效果 */
.lu-chinese-short.interactive {
  cursor: pointer;
  user-select: none;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-all);
  pointer-events: auto;
}

.lu-chinese-short.interactive:hover {
  background-color: var(--color-background-inverse) !important;
  color: var(--color-text-inverse) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.lu-chinese-short.interactive:active {
  transform: translateY(0);
  background-color: #f0f0f0 !important;
}

/* === 重排序动画样式 === */
.lu-tooltip.animating {
  /* 在动画期间保持容器稳定 */
  position: relative;
}

.lu-chinese-short.animating {
  /* 确保动画元素在正确的层级 */
  position: relative;
  z-index: 1;
}

/* 禁用动画期间的hover和active效果 */
.lu-chinese-short.animating:hover {
  transform: none !important;
  background-color: transparent !important;
  color: inherit !important;
  box-shadow: none !important;
}

.lu-chinese-short.animating:active {
  transform: none !important;
  background-color: transparent !important;
}

/* 点击反馈动画 */
.lu-chinese-short.interactive:active {
  transform: scale(1.05) translateY(0);
  transition: transform 0.1s ease-out;
}

/* 防止动画期间的意外交互 */
.lu-tooltip.animating .lu-chinese-short.interactive {
  pointer-events: none;
}

.lu-tooltip:not(.animating) .lu-chinese-short.interactive {
  pointer-events: auto;
}

/* 分隔符样式 */
.lu-separator {
  margin: 0 var(--space-sm);
  opacity: 0.6;
}

/* === 基础Tooltip状态变体 === */
.lu-tooltip.loading {
  opacity: 0.7;
  pointer-events: none;
}

.lu-tooltip.error {
  background: rgba(220, 38, 38, 0.2);
  border-color: rgba(220, 38, 38, 0.3);
}

.lu-tooltip.success {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

/* === 可访问性样式 === */
.lu-tooltip:focus {
  outline: 2px solid var(--color-border-glass);
  outline-offset: 2px;
}



/* === DynamicTooltip 骨架屏样式 === */
.lu-tooltip-skeleton {
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);
  background: var(--color-background-glass);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--radius-lg);
  padding: var(--space-xs) var(--space-md);
  display: inline-block;
  white-space: nowrap;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  font-family: var(--font-sans);
  user-select: none;
}

.lu-tooltip-skeleton[data-theme="light"] {
  background: rgba(240, 240, 240, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.lu-skeleton-text {
  display: inline-block;
  height: 16px;
  background: var(--color-text-muted);
  border-radius: var(--radius-sm);
  position: relative;
  overflow: hidden;
}

.lu-skeleton-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-text-subtle),
    transparent
  );
  animation: skeleton-loading 1.5s infinite;
}

.lu-skeleton-animated .lu-skeleton-text::after {
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 骨架屏尺寸变体 */
.lu-skeleton-short {
  width: 40px;
}

.lu-skeleton-medium {
  width: 80px;
}

.lu-skeleton-long {
  width: 120px;
}

.lu-skeleton-word-text {
  width: 60px;
}

.lu-skeleton-phonetic-text {
  width: 80px;
}

.lu-skeleton-definition-text {
  width: 100px;
}

.lu-skeleton-pos {
  width: 30px;
  height: 14px;
}

/* 骨架屏布局变体 */
.lu-tooltip-skeleton[data-variant="full"] {
  display: block;
  width: 250px;
  padding: var(--space-sm);
  white-space: normal;
}

.lu-skeleton-header {
  margin-bottom: var(--space-sm);
}

.lu-skeleton-word {
  margin-bottom: var(--space-xs);
}

.lu-skeleton-phonetic {
  margin-bottom: var(--space-sm);
}

.lu-skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.lu-skeleton-definition {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.lu-skeleton-separator {
  margin: 0 var(--space-xs);
  color: var(--color-text-muted);
  opacity: 0.3;
}

/* === DynamicTooltip 错误状态样式 === */
.lu-tooltip-error {
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);
  background: var(--color-semantic-danger);
  border: 1px solid var(--color-semantic-danger);
  border-radius: var(--radius-lg);
  padding: var(--space-xs) var(--space-md);
  display: inline-block;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-inverse);
  font-family: var(--font-sans);
  user-select: none;
}

.lu-error-minimal {
  white-space: nowrap;
}

.lu-error-standard {
  white-space: nowrap;
  max-width: 200px;
}

.lu-error-detailed {
  display: block;
  white-space: normal;
  max-width: 300px;
  padding: var(--space-sm) var(--space-md);
}

.lu-error-icon {
  margin-right: var(--space-xs);
  font-size: 1.1em;
}

.lu-error-word {
  font-weight: var(--font-weight-medium);
  margin-left: var(--space-xs);
}

.lu-error-message {
  color: var(--color-text-inverse);
}

.lu-error-retry-btn {
  background: transparent;
  border: 1px solid var(--color-text-inverse);
  color: var(--color-text-inverse);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  margin-left: var(--space-sm);
  font-size: var(--font-size-xs);
  transition: var(--transition-fast);
}

.lu-error-retry-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.lu-error-retry-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 详细错误状态布局 */
.lu-error-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-semibold);
}

.lu-error-title {
  margin-left: var(--space-xs);
}

.lu-error-content {
  margin: var(--space-sm) 0;
}

.lu-error-word-info,
.lu-error-details {
  margin: var(--space-xs) 0;
  font-size: var(--font-size-sm);
}

.lu-error-label {
  font-weight: var(--font-weight-medium);
  margin-right: var(--space-sm);
  opacity: 0.9;
}

.lu-error-text,
.lu-error-detail-text {
  opacity: 0.95;
}

.lu-error-actions {
  margin-top: var(--space-sm);
  text-align: right;
}

.lu-error-actions .lu-error-retry-btn {
  margin-left: 0;
  padding: var(--space-xs) var(--space-md);
}

/* === 回退内容样式 === */
.lu-tooltip.lu-fallback {
  opacity: 0.8;
  font-style: italic;
  border-style: dashed;
}

/* === 主题变体 === */
.lu-tooltip-skeleton[data-theme="light"],
.lu-tooltip-error[data-theme="light"] {
  background: rgba(240, 240, 240, 0.9);
  border-color: rgba(0, 0, 0, 0.2);
  color: var(--color-text-inverse);
}

.lu-tooltip-error[data-theme="light"] {
  background: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.3);
  color: var(--color-semantic-danger);
}

.lu-tooltip-error[data-theme="light"] .lu-error-retry-btn {
  border-color: var(--color-semantic-danger);
  color: var(--color-semantic-danger);
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .lu-tooltip {
    font-size: var(--font-size-xs);
    padding: var(--space-xs) var(--space-sm);
  }

  .lu-pos {
    margin-right: 1px;
  }

  .lu-chinese-short {
    margin-right: 2px;
  }

  .lu-tooltip-skeleton,
  .lu-tooltip-error {
    font-size: var(--font-size-xs);
    padding: var(--space-xs) var(--space-sm);
  }

  .lu-tooltip-skeleton[data-variant="full"] {
    width: 200px;
    padding: var(--space-sm);
  }

  .lu-error-detailed {
    max-width: 250px;
    padding: var(--space-sm) var(--space-sm);
  }

  .lu-skeleton-text {
    height: 14px;
  }
}

/* === 减少动画效果模式 === */
@media (prefers-reduced-motion: reduce) {
  .lu-tooltip {
    transition: none;
  }

  .lu-skeleton-text::after {
    animation: none;
  }

  .lu-error-retry-btn {
    transition: none;
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  .lu-tooltip {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
  }

  .lu-tooltip-skeleton {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 255, 255, 0.9);
  }

  .lu-tooltip-error {
    background: rgba(220, 38, 38, 0.95);
    border-color: rgba(255, 255, 255, 0.9);
  }

  .lu-skeleton-text {
    background: var(--color-text-subtle);
  }
}

/* === 打印样式 === */
@media print {
  .lu-tooltip {
    background: white !important;
    border: 1px solid black !important;
    color: black !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
  }

  .lu-word,
  .lu-definition {
    color: black !important;
  }

  .lu-tooltip-skeleton,
  .lu-tooltip-error {
    background: white !important;
    border: 1px solid black !important;
    color: black !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  .lu-skeleton-text {
    background: #ccc !important;
  }

  .lu-skeleton-text::after {
    display: none;
  }

  .lu-error-retry-btn {
    border-color: black !important;
    color: black !important;
  }
}`;
  }
}

// 导出单例实例
export const styleManager = new StyleManager();