/**
 * 输入验证工具函数
 * 提供常用的表单验证功能
 */

/**
 * 验证邮箱格式
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * 验证密码强度
 */
export const validatePassword = (password: string): boolean => {
  // 至少8位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * 验证密码长度（简单版本）
 */
export const validatePasswordLength = (password: string, minLength: number = 6): boolean => {
  return password.length >= minLength;
};

/**
 * 验证两次密码是否一致
 */
export const validatePasswordMatch = (password: string, confirmPassword: string): boolean => {
  return password === confirmPassword;
};

/**
 * 验证必填字段
 */
export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

/**
 * 综合验证结果类型
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * 邮箱验证（返回详细结果）
 */
export const validateEmailWithMessage = (email: string): ValidationResult => {
  if (!validateRequired(email)) {
    return { isValid: false, error: '请输入邮箱地址' };
  }
  
  if (!validateEmail(email)) {
    return { isValid: false, error: '请输入有效的邮箱地址' };
  }
  
  return { isValid: true };
};

/**
 * 密码验证（返回详细结果）
 */
export const validatePasswordWithMessage = (password: string, minLength: number = 6): ValidationResult => {
  if (!validateRequired(password)) {
    return { isValid: false, error: '请输入密码' };
  }
  
  if (!validatePasswordLength(password, minLength)) {
    return { isValid: false, error: `密码长度至少${minLength}位` };
  }
  
  return { isValid: true };
};

/**
 * 确认密码验证（返回详细结果）
 */
export const validateConfirmPasswordWithMessage = (
  password: string, 
  confirmPassword: string
): ValidationResult => {
  if (!validateRequired(confirmPassword)) {
    return { isValid: false, error: '请确认密码' };
  }
  
  if (!validatePasswordMatch(password, confirmPassword)) {
    return { isValid: false, error: '两次输入的密码不一致' };
  }
  
  return { isValid: true };
};

/**
 * 表单验证钩子类型
 */
export interface FormValidation {
  email?: ValidationResult;
  password?: ValidationResult;
  confirmPassword?: ValidationResult;
}

/**
 * 登录表单验证
 */
export const validateLoginForm = (email: string, password: string): FormValidation => {
  return {
    email: validateEmailWithMessage(email),
    password: validatePasswordWithMessage(password)
  };
};

/**
 * 注册表单验证  
 */
export const validateRegisterForm = (
  email: string, 
  password: string, 
  confirmPassword: string
): FormValidation => {
  return {
    email: validateEmailWithMessage(email),
    password: validatePasswordWithMessage(password),
    confirmPassword: validateConfirmPasswordWithMessage(password, confirmPassword)
  };
};