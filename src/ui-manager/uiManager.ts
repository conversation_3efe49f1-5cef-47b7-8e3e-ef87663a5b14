/**
 * UIManager - 简化的Shadow DOM管理器
 * 使用<lucid>自定义标签，样式通过外部CSS控制
 */

import React from 'react';
import { ShadowView } from './ShadowView';
import { StyleManager } from '../styles/StyleManager';
import { TOOLTIP_DARK_THEME_CSS, TOOLTIP_LIGHT_THEME_CSS } from './tooltip-styles';
import { SLIDER_CSS } from './slider-styles';
import { SUBTITLE_OVERLAY_CSS } from './subtitle-styles';
import { SETTINGS_PANEL_CSS } from './settings-panel-styles';
import {
  ShadowViewInstance,
  TooltipOptions,
  ToolfullOptions,
  SliderOptions,
  ModalOptions,
  LucidSliderOptions,
  Position,
  DEFAULT_TOOLTIP_OPTIONS,
  DEFAULT_TOOLFULL_OPTIONS
} from './types';

export class UIManager {
  private static instance: UIManager | null = null;
  private instances = new Map<string, ShadowViewInstance>();
  private idCounter = 0;
  private styleManager = new StyleManager();

  private constructor() {
    this.setupGlobalEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): UIManager {
    if (!UIManager.instance) {
      UIManager.instance = new UIManager();
    }
    return UIManager.instance;
  }

  // === 高级语义化API ===

  /**
   * 显示Tooltip
   */
  async showTooltip(options: TooltipOptions): Promise<string> {
    const config = { ...DEFAULT_TOOLTIP_OPTIONS, ...options };
    const id = config.id || this.generateId('tooltip');

    // 先隐藏已存在的tooltip
    await this.hideTooltips();

    const component = typeof config.content === 'string'
      ? React.createElement('span', null, config.content)
      : config.content;

    const shadowViewOptions = {
      id: `tooltip-${id}`,
      position: config.position,
      component,
      styles: [this.getTooltipStyles(config.theme)],
      closeOnClickOutside: config.closeOnClickOutside,
      closeOnEscape: config.closeOnEscape,
      className: 'lucid-tooltip'
    };

    return this.createAndShowView(shadowViewOptions);
  }

  /**
   * 显示Toolfull（详细信息窗口）
   */
  async showToolfull(options: ToolfullOptions): Promise<string> {
    const config = { ...DEFAULT_TOOLFULL_OPTIONS, ...options };
    const id = config.id || this.generateId('toolfull');

    // 先隐藏已存在的tooltip
    await this.hideTooltips();

    const component = this.createToolfullComponent(config);

    const shadowViewOptions = {
      id: `toolfull-${id}`,
      position: config.position,
      component,
      styles: [this.getToolfullStyles()],
      closeOnClickOutside: config.closeOnClickOutside,
      closeOnEscape: config.closeOnEscape,
      className: 'lucid-tooltip'
    };

    return this.createAndShowView(shadowViewOptions);
  }

  /**
   * 显示Slider（滑动窗口）
   */
  async showSlider(options: SliderOptions): Promise<string> {
    const id = options.id || this.generateId('slider');

    const component = this.createSliderComponent(options);

    const shadowViewOptions = {
      id: `slider-${id}`,
      position: options.position,
      component,
      styles: [this.getSliderStyles()],
      closeOnClickOutside: options.closeOnClickOutside ?? false,
      closeOnEscape: options.closeOnEscape,
      className: 'lucid-popup'
    };

    return this.createAndShowView(shadowViewOptions);
  }

  /**
   * 显示Modal（模态框）
   */
  async showModal(options: ModalOptions): Promise<string> {
    const id = options.id || this.generateId('modal');

    const component = this.createModalComponent(options);

    // Modal的位置计算
    const position = options.centered
      ? this.getCenteredPosition(options.width, options.height)
      : options.position;

    // 如果有背景遮罩，先创建背景
    if (options.backdrop !== false) {
      await this.createModalBackdrop(id);
    }

    const shadowViewOptions = {
      id: `modal-${id}`,
      position,
      component,
      styles: [this.getModalStyles()],
      closeOnClickOutside: options.closeOnClickOutside,
      closeOnEscape: options.closeOnEscape,
      className: 'lucid-modal'
    };

    return this.createAndShowView(shadowViewOptions);
  }

  /**
   * 创建通用的Shadow DOM视图
   * 用于字幕翻译等新功能组件
   */
  createShadowView(options: {
    component: React.ComponentType<any>;
    props?: any;
    containerId: string;
    styles?: string;
    position?: Position;
    className?: string;
  }): ShadowView {
    const shadowView = new ShadowView({
      id: options.containerId,
      component: React.createElement(options.component, options.props || {}),
      position: options.position || { x: 0, y: 0 },
      styles: options.styles ? [options.styles] : [],
      className: options.className || ''
    });

    // 注册到实例管理器
    const instance: ShadowViewInstance = {
      id: options.containerId,
      view: shadowView,
      isVisible: true,
      createdAt: Date.now()
    };

    this.instances.set(options.containerId, instance);
    shadowView.show();

    return shadowView;
  }

  /**
   * 显示 Lucid Slider（设置面板）
   */
  async showLucidSlider(options: LucidSliderOptions = {}): Promise<string> {
    const id = options.id || this.generateId('lucid-slider');

    // 先隐藏已存在的slider
    await this.hideLucidSlider();

    // 动态导入 Slider 组件以避免循环依赖
    const { Slider } = await import('../components/Slider');

    const component = React.createElement(Slider, {
      isOpen: options.isOpen ?? true,
      onClose: options.onClose || (() => this.hideLucidSlider()),
      className: options.className || '',
    });

    // Lucid Slider 是右边定位的，不需要特定位置
    const shadowViewOptions = {
      id: `lucid-slider-${id}`,
      position: { x: 0, y: 0 }, // 位置由CSS控制
      component,
      styles: [SLIDER_CSS], // 注入完整的Slider CSS
      closeOnClickOutside: false, // 由组件内部控制
      closeOnEscape: false, // 由组件内部控制
      className: 'lucid-slider-host'
    };

    return this.createAndShowView(shadowViewOptions);
  }

  /**
   * 隐藏 Lucid Slider
   */
  async hideLucidSlider(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [id, instance] of this.instances) {
      if (id.includes('lucid-slider')) {
        promises.push(this.hide(id));
      }
    }

    await Promise.all(promises);
  }

  // === 管理API ===

  /**
   * 隐藏指定ID的UI
   */
  async hide(id: string): Promise<void> {
    const instance = this.instances.get(id);
    if (!instance) return;

    instance.view.hide();
    await this.cleanup(id);
  }

  /**
   * 隐藏所有tooltip
   */
  async hideTooltips(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [id, instance] of this.instances) {
      if (id.includes('tooltip') || id.includes('toolfull')) {
        promises.push(this.hide(id));
      }
    }

    await Promise.all(promises);
  }

  /**
   * 根据类型隐藏UI元素
   */
  async hideType(type: string): Promise<void> {
    switch (type) {
      case 'tooltip':
        this.hideTooltips();
        break;
      case 'slider':
        this.hideLucidSlider();
        break;
      case 'modal':
        // 隐藏所有模态框
        const modals = document.querySelectorAll('[data-lucid-modal]');
        modals.forEach(modal => {
          if (modal.parentElement) {
            modal.parentElement.removeChild(modal);
          }
        });
        break;
      case 'all':
        this.hideAll();
        break;
      default:
        console.warn(`Unknown UI type: ${type}`);
    }
  }

  /**
   * 隐藏所有UI
   */
  async hideAll(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const id of this.instances.keys()) {
      promises.push(this.hide(id));
    }

    await Promise.all(promises);
  }

  /**
   * 清理指定实例
   */
  async cleanup(id: string): Promise<void> {
    const instance = this.instances.get(id);
    if (!instance) return;

    instance.view.destroy();
    this.instances.delete(id);

    // 清理Modal背景
    if (id.includes('modal')) {
      this.removeModalBackdrop(id);
    }
  }

  // === 查询API ===

  /**
   * 检查指定ID的UI是否存在且可见
   */
  isVisible(id: string): boolean {
    const instance = this.instances.get(id);
    return instance ? instance.isVisible : false;
  }

  /**
   * 获取所有实例统计
   */
  getStats(): { total: number; tooltips: number; modals: number; sliders: number } {
    let tooltips = 0;
    let modals = 0;
    let sliders = 0;

    for (const [id] of this.instances) {
      if (id.includes('tooltip') || id.includes('toolfull')) {
        tooltips++;
      } else if (id.includes('modal')) {
        modals++;
      } else if (id.includes('slider')) {
        sliders++;
      }
    }

    return {
      total: this.instances.size,
      tooltips,
      modals,
      sliders
    };
  }

  // === 私有方法 ===

  /**
   * 创建并显示ShadowView实例
   */
  private async createAndShowView(options: any): Promise<string> {
    const view = new ShadowView(options);

    const instance: ShadowViewInstance = {
      id: options.id,
      view,
      isVisible: true,
      createdAt: Date.now()
    };

    this.instances.set(options.id, instance);
    view.show();

    return options.id;
  }

  /**
   * 生成唯一ID
   */
  private generateId(type: string): string {
    return `${type}-${++this.idCounter}-${Date.now()}`;
  }

  /**
   * 获取居中位置
   */
  private getCenteredPosition(width = 400, height = 300): Position {
    return {
      x: (window.innerWidth - width) / 2,
      y: (window.innerHeight - height) / 2
    };
  }

  /**
   * 设置全局事件监听器
   */
  private setupGlobalEventListeners(): void {
    // 页面卸载时清理所有实例
    window.addEventListener('beforeunload', () => {
      this.hideAll();
    });

    // 页面隐藏时隐藏所有UI
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.hideAll();
      }
    });
  }

  // === React组件创建方法 ===

  private createToolfullComponent(options: ToolfullOptions) {
    const { data } = options;

    return React.createElement('div', { className: 'lucid-toolfull' }, [
      React.createElement('div', { key: 'word', className: 'word-title' }, options.word),
      data.pronunciation && React.createElement('div', { key: 'pronunciation', className: 'pronunciation' }, `[${data.pronunciation}]`),
      React.createElement('div', { key: 'definitions', className: 'definitions' },
        data.definitions.map((def, i) =>
          React.createElement('div', { key: i, className: 'definition' }, `${i + 1}. ${def}`)
        )
      ),
      data.examples.length > 0 && React.createElement('div', { key: 'examples', className: 'examples' }, [
        React.createElement('div', { key: 'title', className: 'examples-title' }, 'Examples:'),
        ...data.examples.map((example, i) =>
          React.createElement('div', { key: i, className: 'example' }, example)
        )
      ]),
      data.etymology && React.createElement('div', { key: 'etymology', className: 'etymology' }, [
        React.createElement('div', { key: 'title', className: 'etymology-title' }, 'Etymology:'),
        React.createElement('div', { key: 'content' }, data.etymology)
      ])
    ]);
  }

  private createSliderComponent(options: SliderOptions) {
    return React.createElement('div', { className: 'lucid-slider' }, [
      React.createElement('div', { key: 'header', className: 'slider-header' }, [
        React.createElement('h3', { key: 'title' }, options.title),
        React.createElement('button', {
          key: 'close',
          className: 'close-btn',
          onClick: () => this.hide(options.id!)
        }, '×')
      ]),
      React.createElement('div', { key: 'content', className: 'slider-content' }, options.content)
    ]);
  }

  private createModalComponent(options: ModalOptions) {
    return React.createElement('div', { className: 'lucid-modal' }, [
      React.createElement('div', { key: 'header', className: 'modal-header' }, [
        React.createElement('h3', { key: 'title' }, options.title),
        React.createElement('button', {
          key: 'close',
          className: 'close-btn',
          onClick: () => this.hide(options.id!)
        }, '×')
      ]),
      React.createElement('div', { key: 'content', className: 'modal-content' }, options.content)
    ]);
  }

  // === 样式定义 ===

  private getTooltipStyles(theme: 'dark' | 'light' = 'dark'): string {
    // 使用新的 StyleManager 系统
    return this.styleManager.getShadowDOMCSS(['tooltip'], theme);
  }

  private getToolfullStyles(): string {
    return `
      .lucid-toolfull {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 16px;
        max-width: 400px;
        max-height: 500px;
        overflow-y: auto;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        font-family: system-ui, sans-serif;
      }
      
      .word-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #2563eb;
      }
      
      .pronunciation {
        font-style: italic;
        color: #6b7280;
        margin-bottom: 12px;
      }
      
      .definition {
        margin: 6px 0;
        line-height: 1.5;
      }
      
      .examples-title, .etymology-title {
        font-weight: bold;
        margin: 12px 0 6px 0;
        color: #374151;
      }
      
      .example {
        margin: 4px 0;
        padding-left: 12px;
        color: #6b7280;
        font-style: italic;
      }
      
      .etymology {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #e5e7eb;
        font-size: 13px;
        color: #6b7280;
      }
    `;
  }

  private getSliderStyles(): string {
    return `
      .lucid-slider {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        min-width: 300px;
        max-width: 500px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        overflow: hidden;
      }
      
      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .slider-header h3 {
        margin: 0;
        font-size: 16px;
        color: #374151;
      }
      
      .close-btn {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #6b7280;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .close-btn:hover {
        color: #374151;
      }
      
      .slider-content {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;
      }
    `;
  }

  private getModalStyles(): string {
    return `
      .lucid-modal {
        background: white;
        border-radius: 8px;
        width: 400px;
        max-height: 80vh;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .modal-header h3 {
        margin: 0;
        font-size: 18px;
        color: #374151;
      }
      
      .modal-content {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
      }
    `;
  }

  /**
   * 创建Modal背景遮罩
   */
  private async createModalBackdrop(modalId: string): Promise<void> {
    const backdrop = document.createElement('div');
    backdrop.id = `modal-backdrop-${modalId}`;
    backdrop.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      backdrop-filter: blur(2px);
    `;

    backdrop.addEventListener('click', () => {
      this.hide(`modal-${modalId}`);
    });

    document.body.appendChild(backdrop);
  }

  /**
   * 移除Modal背景遮罩
   */
  private removeModalBackdrop(modalId: string): void {
    const backdrop = document.getElementById(`modal-backdrop-${modalId.replace('modal-', '')}`);
    if (backdrop) {
      backdrop.remove();
    }
  }
}

// 导出单例实例
export const uiManager = UIManager.getInstance();