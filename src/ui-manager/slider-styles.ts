import { styleManager } from '../styles/StyleManager';

/**
 * Slider样式生成器
 * 替代原有的硬编码CSS字符串，使用StyleManager统一管理
 */
class SliderStyleGenerator {
  private styleManager = styleManager;

  /**
   * 生成完整的Slider CSS字符串用于Shadow DOM注入
   */
  generateSliderCSS(): string {
    const parts = [
      this.generateImports(),
      this.styleManager.generateCSSVariables(),
      this.generateHostStyles(),
      this.generateSliderBaseStyles(),
      this.generateSliderHeaderStyles(),
      this.generateSliderContentStyles(),
      this.generateCardStyles(),
      this.generateStatsStyles(),
      this.generateWordListStyles(),
      this.generateSettingsStyles(),
      this.generateLoginStyles(),
      this.generateSwitchStyles(),
      this.generateFooterStyles(),
      this.generateScrollbarStyles(),
    ];

    return parts.join('\n\n');
  }

  private generateImports(): string {
    return `/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap');`;
  }

  private generateHostStyles(): string {
    return `/* Shadow DOM Host Styles */
:host {
  font-family: var(--font-sans);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-secondary);
}

:host * {
  font-family: inherit;
  font-weight: inherit;
}

.lu-open-slider-btn {
  padding: 10px 20px;
  border-radius: var(--radius-lg);
  border: none;
  background-color: var(--color-brand-primary);
  color: white;
  cursor: pointer;
  font-family: var(--font-sans);
  font-weight: var(--font-weight-normal);
}`;
  }

  private generateSliderBaseStyles(): string {
    return `/* Slider Container */
.lu-slide {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  z-index: 2147483647;
  backdrop-filter: blur(28px) saturate(150%);
  -webkit-backdrop-filter: blur(28px) saturate(150%);
  background: rgba(28, 28, 30, 0.92);
  border-left: 1px solid;
  border-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6);
  border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
  transition: transform var(--transition-slow), opacity var(--transition-slow);
  transform: translateX(0);
  opacity: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--font-sans);
  font-weight: var(--font-weight-normal);
}

.lu-slide * {
  font-family: inherit;
  font-weight: inherit;
}

.lu-slide.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

@media (max-width: 767px) {
  .lu-slide {
    width: 100vw;
    border-radius: 0;
    border-left: none;
  }
}`;
  }

  private generateSliderHeaderStyles(): string {
    return `/* Header */
.lu-slider-header {
  flex-shrink: 0;
  padding: var(--space-xl) var(--space-2xl);
  border-bottom: 1px solid;
  border-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.lu-brand {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.lu-logo {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--color-brand-primary), #ff9d57);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
}

.lu-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.lu-vip-tag {
  padding: var(--space-xs) var(--space-sm);
  background: var(--color-status-vip-background);
  color: var(--color-status-vip-text);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  border-radius: var(--radius-md);
  text-transform: uppercase;
  border: 1px solid var(--color-status-vip-border);
}

.lu-header-button {
  width: var(--space-4xl);
  height: var(--space-4xl);
  border: none;
  background: transparent;
  color: var(--color-text-disabled);
  cursor: pointer;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all);
}

.lu-header-button:hover {
  background: var(--color-bg-hover);
  color: var(--color-text-primary);
  transform: scale(1.1);
}

.lu-slider-header #header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}`;
  }

  private generateSliderContentStyles(): string {
    return `/* Content Area */
.lu-slider-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--space-xl) var(--space-2xl);
}`;
  }

  private generateCardStyles(): string {
    return `/* Cards */
.lu-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  transition: var(--transition-all);
}

.lu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.3);
  border-color: var(--color-border-hover);
}`;
  }

  private generateStatsStyles(): string {
    return `/* Stats */
.lu-stats-group {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.lu-stat-card {
  flex: 1;
  padding: var(--space-lg);
  text-align: center;
  cursor: pointer;
}

.lu-stat-number {
  color: #ffffff;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-xs);
}

.lu-stat-title {
  color: var(--color-text-disabled);
  font-size: var(--font-size-sm);
}`;
  }

  private generateWordListStyles(): string {
    return `/* Word List */
.lu-word-section .lu-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.lu-section-title {
  color: #f0f0f0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.lu-word-count {
  color: var(--color-text-disabled);
  font-size: var(--font-size-base);
}

.lu-word-item {
  padding: 0;
  cursor: pointer;
  background: var(--color-bg-primary);
}

.lu-word-item-header {
  padding: var(--space-md) var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lu-word-item-header:hover .lu-word-text {
  color: var(--color-brand-primary);
}

.lu-word-text-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.lu-word-text {
  color: #f0f0f0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.lu-expand-icon {
  color: var(--color-text-disabled);
  transition: transform var(--transition-slow);
}

.lu-word-item.expanded .lu-expand-icon {
  transform: rotate(180deg);
}

.lu-word-actions {
  display: flex;
  gap: var(--space-sm);
}

.lu-word-action {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--color-text-disabled);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all);
}

.lu-word-action:hover {
  background: var(--color-bg-hover);
  transform: scale(1.1);
  color: var(--color-text-primary);
}

.lu-word-action.favorite.active {
  color: var(--color-semantic-danger);
}

.lu-word-action.master.active {
  color: var(--color-semantic-success);
}

/* Word Details - 单词详情展开样式 */
.lu-word-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-slow), padding var(--transition-slow);
  padding: 0 var(--space-lg);
  border-top: 1px solid transparent;
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.lu-word-item.expanded .lu-word-details {
  max-height: 600px;
  padding: var(--space-lg);
  border-top-color: var(--color-border-primary);
}

.lu-word-detail-item {
  display: flex;
  gap: var(--space-lg);
  align-items: flex-start;
}

.lu-word-detail-item strong {
  color: var(--color-text-disabled);
  font-weight: var(--font-weight-semibold);
  margin-right: 0;
  display: inline-block;
  width: auto;
  min-width: 60px;
  text-transform: capitalize;
  flex-shrink: 0;
}

/* Phonetic Styles - 音标样式 */
.lu-phonetic {
  font-family: "Lucida Sans Unicode", "Arial Unicode MS", sans-serif;
}

.lu-phonetic-group div {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.lu-phonetic-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.lu-phonetic-label {
  background-color: var(--color-bg-hover);
  color: var(--color-text-muted);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px var(--space-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border-secondary);
}

/* Definition Styles - 释义样式 */
.lu-pos-section .lu-definitions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.lu-definition-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.lu-definition-cn {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-sm);
}

.lu-definition-en {
  color: var(--color-text-disabled);
  line-height: var(--line-height-normal);
  font-size: var(--font-size-sm);
}

/* Word Forms Grid - 词形变化网格 */
.lu-word-forms-grid {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: var(--space-sm) var(--space-lg);
  width: 100%;
}

.lu-word-form-name {
  color: var(--color-text-disabled);
}

.lu-word-form-value {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}`;
  }

  private generateSettingsStyles(): string {
    return `/* Settings */
.lu-settings-section {
  margin-bottom: var(--space-2xl);
}

.lu-settings-header {
  font-size: var(--font-size-sm);
  color: var(--color-text-disabled);
  font-weight: var(--font-weight-medium);
  padding-left: var(--space-lg);
  margin-bottom: var(--space-sm);
}

.lu-settings-card {
  padding: var(--space-sm);
}

.lu-setting-item {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-colors);
}

.lu-settings-card .lu-setting-item:not(:last-child) {
  border-bottom: 1px solid var(--color-bg-overlay);
}

.lu-setting-item:hover {
  background-color: var(--color-bg-overlay);
}

.lu-setting-icon {
  width: var(--space-xl);
  height: var(--space-xl);
  color: #c0c0c0;
  margin-right: var(--space-lg);
}

.lu-setting-title {
  flex: 1;
  color: #f0f0f0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.lu-setting-value {
  color: var(--color-text-disabled);
  font-size: var(--font-size-base);
  margin-right: var(--space-sm);
}

.lu-setting-arrow {
  width: var(--space-lg);
  height: var(--space-lg);
  color: var(--color-text-subtle);
}

.lu-user-info {
  display: flex;
  align-items: center;
}

.lu-user-info .lu-setting-value {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.lu-user-info .lu-verified-icon {
  color: var(--color-semantic-success);
}

.lu-account-view .lu-setting-item {
  padding: var(--space-lg) var(--space-sm);
}

.lu-account-view .lu-setting-value {
  color: #f0f0f0;
}

.lu-account-view .lu-membership-tag {
  font-size: var(--font-size-sm);
  padding: 2px var(--space-sm);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-text-subtle);
  color: var(--color-text-disabled);
}

.lu-vip-tag.early-bird,
.lu-account-view .lu-membership-tag.early-bird {
  color: var(--color-status-earlyBird-text);
  border-color: var(--color-status-earlyBird-border);
  background-color: var(--color-status-earlyBird-background);
}

.lu-logout-button {
  width: 100%;
  padding: 14px;
  border: none;
  background-color: rgba(45, 45, 48, 0.9);
  color: var(--color-semantic-danger);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: var(--transition-colors);
  margin-top: var(--space-2xl);
}

.lu-logout-button:hover {
  background-color: rgba(60, 60, 63, 0.9);
}`;
  }

  private generateLoginStyles(): string {
    return `/* Login Styles */
.lu-login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  box-sizing: border-box;
}

.lu-login-box {
  width: 100%;
  max-width: 360px;
  background: var(--color-bg-secondary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-4xl);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5);
  position: relative;
}

.lu-back-button {
  position: absolute;
  top: var(--space-xl);
  left: var(--space-xl);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-base);
  padding: var(--space-sm);
  font-family: var(--font-sans);
  border-radius: var(--radius-md);
  transition: var(--transition-all);
}

.lu-back-button:hover {
  background: var(--color-bg-hover);
  color: var(--color-text-primary);
}

.lu-social-login-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.lu-social-login-btn {
  width: 100%;
  padding: 10px var(--space-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  transition: var(--transition-all);
  font-family: var(--font-sans);
}

.lu-social-login-btn:hover {
  border-color: var(--color-border-secondary);
}

.lu-social-login-btn svg {
  width: var(--space-xl);
  height: var(--space-xl);
}

.lu-social-login-btn.google,
.lu-social-login-btn.github {
  color: #fff;
  background-color: #333;
}

.lu-social-login-btn.google:hover,
.lu-social-login-btn.github:hover {
  background-color: #444;
}

.lu-email-form {
  display: flex;
  flex-direction: column;
  margin-top: var(--space-md);
}

.lu-email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: var(--space-md);
}

.lu-email-input-wrapper:first-child {
  margin-top: 0;
}

.lu-email-input-wrapper svg {
  position: absolute;
  left: 14px;
  color: var(--color-text-subtle);
}

.lu-email-input {
  width: 100%;
  box-sizing: border-box;
  background-color: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg) var(--space-md) 44px;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  outline: none;
  transition: var(--transition-all);
  font-family: var(--font-sans);
}

.lu-email-input::placeholder {
  color: var(--color-text-subtle);
}

.lu-email-input:focus {
  border-color: var(--color-brand-primary);
  box-shadow: 0 0 0 3px var(--color-border-focus);
}

.lu-send-code-btn {
  width: 100%;
  padding: var(--space-md);
  border: none;
  background-color: var(--color-brand-primary);
  color: white;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-colors);
  margin-top: var(--space-lg);
  font-family: var(--font-sans);
}

.lu-send-code-btn:hover {
  background-color: var(--color-brand-light);
}

.lu-send-code-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.lu-login-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-lg);
}

.lu-register-links {
  text-align: center;
  margin-top: var(--space-lg);
}`;
  }

  private generateSwitchStyles(): string {
    return `/* Switch */
.lu-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: var(--transition-colors);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.lu-switch.active {
  background: var(--color-brand-primary);
}

.lu-switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: var(--space-xl);
  height: var(--space-xl);
  background: #ffffff;
  border-radius: var(--radius-full);
  transition: var(--transition-transform);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.lu-switch.active .lu-switch-thumb {
  transform: translateX(var(--space-xl));
}`;
  }

  private generateFooterStyles(): string {
    return `/* Footer */
.lu-slider-footer {
  flex-shrink: 0;
  border-top: 1px solid;
  border-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  padding: var(--space-md) var(--space-2xl);
  display: flex;
  gap: var(--space-md);
}

.lu-action-button {
  flex: 1;
  padding: 10px;
  border: none;
  background: transparent;
  color: var(--color-text-disabled);
  cursor: pointer;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  transition: var(--transition-all);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.lu-action-button:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #ffffff;
}

.lu-action-button.active {
  color: var(--color-brand-primary);
  background: rgba(249, 115, 22, 0.15);
}`;
  }

  private generateScrollbarStyles(): string {
    return `/* Scrollbar */
.lu-slider-content::-webkit-scrollbar {
  width: 6px;
}

.lu-slider-content::-webkit-scrollbar-track {
  background: transparent;
}

.lu-slider-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 3px;
}

.lu-slider-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}`;
  }
}

// 导出样式生成器实例和CSS字符串
const sliderStyleGenerator = new SliderStyleGenerator();
export const SLIDER_CSS = sliderStyleGenerator.generateSliderCSS();