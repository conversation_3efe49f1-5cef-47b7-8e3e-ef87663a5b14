import { storage } from 'wxt/utils/storage';
import { ENV } from '../../utils/env';
import type { RefreshTokenResponse, TokenPair } from '../../types/auth';

/**
 * API客户端 - 处理HTTP请求、认证和令牌刷新
 */
export class APIClient {
  private baseURL: string;
  private isRefreshing = false;
  private refreshPromise: Promise<boolean> | null = null;

  // 存储键配置
  private readonly storageKeys = {
    accessToken: 'local:auth.accessToken',
    refreshToken: 'local:auth.refreshToken',
    expiresAt: 'local:auth.expiresAt',
    user: 'local:auth.user'
  } as const;

  constructor() {
    this.baseURL = ENV.API_BASE_URL;

    if (ENV.DEBUG_API) {
      console.log('[APIClient] Initialized with baseURL:', this.baseURL);
    }
  }

  /**
   * 获取有效的访问令牌
   */
  private async getValidToken(): Promise<string | null> {
    try {
      const [token, expiresAt] = await Promise.all([
        storage.getItem<string>(this.storageKeys.accessToken),
        storage.getItem<number>(this.storageKeys.expiresAt)
      ]);

      if (!token || !expiresAt) {
        return null;
      }

      // 检查令牌是否过期（提前5分钟刷新）
      const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间
      if (expiresAt <= Date.now() + bufferTime) {
        return null;
      }

      return token;
    } catch (error) {
      console.error('[APIClient] Failed to get valid token:', error);
      return null;
    }
  }

  /**
   * 存储令牌对
   */
  private async setTokens(tokens: TokenPair): Promise<void> {
    try {
      const expiresAt = Date.now() + (tokens.expiresIn * 1000);
      await Promise.all([
        storage.setItem(this.storageKeys.accessToken, tokens.accessToken),
        storage.setItem(this.storageKeys.refreshToken, tokens.refreshToken),
        storage.setItem(this.storageKeys.expiresAt, expiresAt)
      ]);
    } catch (error) {
      console.error('[APIClient] Failed to store tokens:', error);
      throw error;
    }
  }

  /**
   * 清空所有认证相关数据
   */
  private async clearTokens(): Promise<void> {
    try {
      await Promise.all([
        storage.removeItem(this.storageKeys.accessToken),
        storage.removeItem(this.storageKeys.refreshToken),
        storage.removeItem(this.storageKeys.expiresAt),
        storage.removeItem(this.storageKeys.user)
      ]);
    } catch (error) {
      console.error('[APIClient] Failed to clear tokens:', error);
    }
  }

  /**
   * 执行HTTP请求
   * @param endpoint API端点
   * @param options 请求选项
   * @returns Response对象
   */
  async request(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    
    // 获取访问令牌
    const token = await this.getValidToken();
    
    // 构建请求头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // 安全地合并额外的请求头
    if (options.headers) {
      if (options.headers instanceof Headers) {
        options.headers.forEach((value, key) => {
          headers[key] = value;
        });
      } else if (typeof options.headers === 'object') {
        Object.entries(options.headers).forEach(([key, value]) => {
          if (typeof value === 'string') {
            headers[key] = value;
          }
        });
      }
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };

    if (ENV.DEBUG_API) {
      console.log('[APIClient] Making request:', {
        url,
        method: options.method || 'GET',
        hasToken: !!token
      });
    }

    try {
      const response = await fetch(url, requestOptions);

      // 处理401未授权错误
      if (response.status === 401 && token) {
        if (ENV.DEBUG_API) {
          console.log('[APIClient] 401 error, attempting token refresh');
        }

        const refreshed = await this.handleTokenRefresh();
        if (refreshed) {
          // 重试原请求
          const newToken = await this.getValidToken();
          if (newToken) {
            requestOptions.headers = {
              ...requestOptions.headers,
              'Authorization': `Bearer ${newToken}`
            };
            return await fetch(url, requestOptions);
          }
        }
      }

      return response;
    } catch (error) {
      console.error('[APIClient] Request failed:', error);

      // 检查是否是CORS错误
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        const corsError = new Error('无法连接到服务器，请检查网络连接或稍后重试');
        corsError.name = 'NetworkError';
        throw corsError;
      }

      throw error;
    }
  }

  /**
   * GET请求
   */
  async get(endpoint: string, options?: RequestInit): Promise<Response> {
    return this.request(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  async post(endpoint: string, data?: any, options?: RequestInit): Promise<Response> {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT请求
   */
  async put(endpoint: string, data?: any, options?: RequestInit): Promise<Response> {
    return this.request(endpoint, {
      ...options,
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE请求
   */
  async delete(endpoint: string, options?: RequestInit): Promise<Response> {
    return this.request(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * 处理令牌刷新
   * @returns 是否刷新成功
   */
  private async handleTokenRefresh(): Promise<boolean> {
    // 防止并发刷新 - 如果正在刷新，等待现有Promise完成
    if (this.isRefreshing && this.refreshPromise) {
      return await this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 执行令牌刷新
   */
  private async performTokenRefresh(): Promise<boolean> {
    try {
      const refreshToken = await storage.getItem<string>(this.storageKeys.refreshToken);
      if (!refreshToken) {
        if (ENV.DEBUG_API) {
          console.log('[APIClient] No refresh token available');
        }
        return false;
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (response.ok) {
        const data: RefreshTokenResponse = await response.json();
        
        if (data.success && data.data) {
          await this.setTokens(data.data);
          
          if (ENV.DEBUG_API) {
            console.log('[APIClient] Token refreshed successfully');
          }
          return true;
        }
      }

      if (ENV.DEBUG_API) {
        console.log('[APIClient] Token refresh failed, clearing tokens');
      }
      
      // 刷新失败，清除所有令牌
      await this.clearTokens();
      return false;
    } catch (error) {
      console.error('[APIClient] Token refresh error:', error);
      await this.clearTokens();
      return false;
    }
  }

  /**
   * 检查API连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      // 创建 AbortController 来实现超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
      
      const response = await fetch(`${this.baseURL}/health`, {
        method: 'GET',
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.error('[APIClient] Connection check failed:', error);
      return false;
    }
  }
}

// 导出单例实例
export const apiClient = new APIClient();