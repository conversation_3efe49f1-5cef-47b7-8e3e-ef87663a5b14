<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip 重排序动画测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            padding: 40px;
            background: #1a1a1a;
            color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        
        h1, h2 {
            color: #fff;
        }
        
        .demo-tooltip {
            display: inline-block;
            margin: 20px;
            padding: 20px;
            background: rgba(30, 30, 30, 0.45);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            backdrop-filter: blur(14px);
        }
        
        .instructions {
            background: #1e3a8a;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #065f46;
            color: #d1fae5;
        }
        
        /* 模拟 StyleManager 的样式 */
        .lu-tooltip {
            backdrop-filter: blur(14px) saturate(160%);
            -webkit-backdrop-filter: blur(14px) saturate(160%);
            background: rgba(30, 30, 30, 0.45);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 4px 12px;
            font-size: 0.88em;
            line-height: 1.45;
            color: #dadada;
            display: inline-block;
            white-space: nowrap;
            transition: color 0.15s ease-out;
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
            user-select: none;
        }
        
        .lu-pos {
            font-weight: 500;
            color: inherit;
            margin-right: 2px;
        }
        
        .lu-chinese-short {
            font-size: 0.9em;
            color: inherit;
            margin-right: 3px;
        }
        
        .lu-chinese-short.interactive {
            cursor: pointer;
            user-select: none;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.15s ease;
            pointer-events: auto;
        }
        
        .lu-chinese-short.interactive:hover {
            background-color: #ffffff !important;
            color: #333333 !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .lu-chinese-short.interactive:active {
            transform: scale(1.05) translateY(0);
            transition: transform 0.1s ease-out;
            background-color: #f0f0f0 !important;
        }
        
        .lu-tooltip.animating {
            position: relative;
        }
        
        .lu-chinese-short.animating {
            position: relative;
            z-index: 1;
        }
        
        .lu-tooltip.animating .lu-chinese-short.interactive {
            pointer-events: none;
        }
        
        .lu-tooltip:not(.animating) .lu-chinese-short.interactive {
            pointer-events: auto;
        }
        
        .lu-separator {
            margin: 0 4px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎬 Tooltip 重排序动画测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>1. 点击下面的中文释义（蓝色可点击文字）</p>
            <p>2. 观察元素重新排序时的平滑动画效果</p>
            <p>3. 多次点击同一个释义，它会逐渐移动到前面</p>
            <p>4. 点击不同词性的释义，观察组间排序</p>
        </div>
        
        <div class="status" id="status">
            ✅ 动画系统已加载，点击释义开始测试
        </div>
        
        <div class="test-section">
            <h2>🎯 测试用例：developed</h2>
            <div class="demo-tooltip">
                <div id="tooltip-container"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 点击统计</h2>
            <div id="click-stats">
                <p>暂无点击记录</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟 Tooltip 数据
        const mockData = {
            word: "developed",
            explain: [
                {
                    pos: "adjective",
                    definitions: [
                        { definition: "having developed", chinese: "发展的", chinese_short: "发展的" },
                        { definition: "mature", chinese: "成熟的", chinese_short: "成熟的" },
                        { definition: "advanced", chinese: "发达的", chinese_short: "发达的" }
                    ]
                },
                {
                    pos: "verb",
                    definitions: [
                        { definition: "to develop", chinese: "发展", chinese_short: "发展" }
                    ]
                }
            ]
        };

        // 模拟偏好存储
        let preferences = {};
        let isAnimating = false;
        let elementsMap = new Map();

        // 模拟 recordPreference
        function recordPreference(word, pos, chineseShort) {
            const key = `${pos}.${chineseShort}`;
            if (!preferences[word]) preferences[word] = {};
            preferences[word][key] = (preferences[word][key] || 0) + 1;
            
            updateClickStats();
            console.log(`📊 Recording preference: ${word} -> ${key} = ${preferences[word][key]}`);
        }

        // 模拟 sortByPreference
        function sortByPreference(word, explain) {
            if (!explain?.length) return explain;
            
            const prefs = preferences[word] || {};
            
            const groupScores = explain.map(group => {
                const maxScore = Math.max(
                    ...group.definitions.map(def => 
                        prefs[`${group.pos}.${def.chinese_short}`] || 0
                    )
                );
                return { ...group, maxScore };
            });
            
            groupScores.sort((a, b) => b.maxScore - a.maxScore);
            
            const sorted = groupScores.map(group => ({
                ...group,
                definitions: group.definitions.slice().sort((a, b) => {
                    const scoreA = prefs[`${group.pos}.${a.chinese_short}`] || 0;
                    const scoreB = prefs[`${group.pos}.${b.chinese_short}`] || 0;
                    return scoreB - scoreA;
                })
            }));
            
            return sorted;
        }

        // FLIP 动画实现
        function performFLIPAnimation() {
            if (!isAnimating) return;

            const container = document.getElementById('tooltip-container');
            if (!container || elementsMap.size === 0) return;

            // 获取所有元素的新位置
            const newPositions = new Map();
            elementsMap.forEach((element, key) => {
                if (element.parentElement) {
                    newPositions.set(key, element.getBoundingClientRect());
                }
            });

            // 应用 FLIP 动画
            elementsMap.forEach((element, key) => {
                const oldPos = element.dataset.oldPosition;
                const newPos = newPositions.get(key);
                
                if (oldPos && newPos) {
                    const oldRect = JSON.parse(oldPos);
                    const deltaX = oldRect.left - newPos.left;
                    const deltaY = oldRect.top - newPos.top;

                    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
                        // 设置初始位置（反转）
                        element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                        element.style.transition = 'none';

                        // 强制重排
                        element.offsetHeight;

                        // 播放动画到最终位置
                        element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                        element.style.transform = 'translate(0, 0)';
                    }
                }

                // 清理
                delete element.dataset.oldPosition;
            });

            // 动画完成后重置状态
            setTimeout(() => {
                isAnimating = false;
                elementsMap.forEach((element) => {
                    element.style.transition = '';
                    element.style.transform = '';
                });
                
                // 移除 animating 类
                const tooltip = document.querySelector('.lu-tooltip');
                if (tooltip) {
                    tooltip.classList.remove('animating');
                }
                
                document.querySelectorAll('.lu-chinese-short').forEach(el => {
                    el.classList.remove('animating');
                });
                
                updateStatus('✅ 动画完成，可以继续点击测试');
            }, 300);
        }

        // 处理点击事件
        function handleDefinitionClick(event, pos, chineseShort) {
            if (isAnimating) return;

            event.stopPropagation();
            event.preventDefault();

            updateStatus('🎬 正在执行重排序动画...');

            // 记录当前所有元素的位置（FLIP 的 First 步骤）
            elementsMap.forEach((element) => {
                if (element.parentElement) {
                    const rect = element.getBoundingClientRect();
                    element.dataset.oldPosition = JSON.stringify({
                        left: rect.left,
                        top: rect.top,
                        width: rect.width,
                        height: rect.height
                    });
                }
            });

            // 添加点击反馈动画
            const clickedElement = event.currentTarget;
            clickedElement.style.transform = 'scale(1.1)';
            clickedElement.style.transition = 'transform 0.1s ease-out';
            
            setTimeout(() => {
                clickedElement.style.transform = '';
                clickedElement.style.transition = '';
            }, 100);

            recordPreference(mockData.word, pos, chineseShort);
            isAnimating = true;
            
            // 重新渲染
            renderTooltip();
            
            // 执行动画
            setTimeout(performFLIPAnimation, 10);
        }

        // 渲染 Tooltip
        function renderTooltip() {
            const container = document.getElementById('tooltip-container');
            const sortedExplain = sortByPreference(mockData.word, mockData.explain);
            
            elementsMap.clear();
            
            const parts = [];
            
            sortedExplain.forEach((item, index) => {
                const posShort = item.pos === "adjective" ? "adj." : 
                               item.pos === "verb" ? "v." : 
                               item.pos.substring(0, 3) + ".";
                
                parts.push(`<span class="lu-pos">${posShort}</span>`);
                
                item.definitions.forEach((def, defIndex) => {
                    const stableKey = `${item.pos}-${def.chinese_short}`;
                    parts.push(`
                        <span 
                            class="lu-chinese-short interactive ${isAnimating ? 'animating' : ''}"
                            data-key="${stableKey}"
                            data-pos="${item.pos}"
                            data-chinese="${def.chinese_short}"
                            style="cursor: pointer; user-select: none;"
                        >
                            ${def.chinese_short}
                        </span>
                    `);
                });
                
                if (index < sortedExplain.length - 1) {
                    parts.push('<span class="lu-separator"> </span>');
                }
            });
            
            container.innerHTML = `
                <span class="lu-tooltip ${isAnimating ? 'animating' : ''}" data-theme="dark">
                    ${parts.join('')}
                </span>
            `;
            
            // 重新绑定事件和引用
            container.querySelectorAll('.lu-chinese-short.interactive').forEach(el => {
                const key = el.dataset.key;
                const pos = el.dataset.pos;
                const chinese = el.dataset.chinese;
                
                elementsMap.set(key, el);
                
                el.addEventListener('click', (e) => handleDefinitionClick(e, pos, chinese));
                el.addEventListener('mousedown', (e) => e.stopPropagation());
                el.addEventListener('mouseup', (e) => e.stopPropagation());
            });
        }

        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 更新点击统计
        function updateClickStats() {
            const statsDiv = document.getElementById('click-stats');
            if (Object.keys(preferences).length === 0) {
                statsDiv.innerHTML = '<p>暂无点击记录</p>';
                return;
            }
            
            let html = '<h3>点击统计：</h3>';
            for (const [word, prefs] of Object.entries(preferences)) {
                html += `<h4>${word}:</h4><ul>`;
                for (const [key, count] of Object.entries(prefs)) {
                    html += `<li>${key}: ${count} 次</li>`;
                }
                html += '</ul>';
            }
            statsDiv.innerHTML = html;
        }

        // 初始化
        renderTooltip();
        updateStatus('✅ 动画系统已加载，点击释义开始测试');
    </script>
</body>
</html>
