{"extends": "./.wxt/tsconfig.json", "compilerOptions": {"allowImportingTsExtensions": true, "jsx": "react-jsx", "baseUrl": ".", "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["vitest/globals", "chrome"], "paths": {"@components/*": ["src/components/*"], "@features/*": ["src/features/*"], "@services/*": ["src/services/*"], "@ui-manager": ["src/ui-manager"], "@ui-manager/*": ["src/ui-manager/*"], "@styles/*": ["src/styles/*"], "@test/*": ["src/test/*"], "@tooltip/*": ["src/components/Tooltip/*"], "@dynamic-tooltip/*": ["src/components/DynamicTooltip/*"], "@dictionary/*": ["src/features/dictionary/*"], "@hooks/*": ["src/hooks/*"], "@types/*": ["src/types/*"]}}}